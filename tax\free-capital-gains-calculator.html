﻿<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="index, follow">
  <title>Capital Gains Tax Calculator India | LTCG STCG | CalculatorSuites</title>
  <meta name="description" content="Calculate capital gains tax on your investments. Get accurate results for short-term and long-term capital gains with exemptions.">
  
  <!-- Favicon -->
  <link rel="icon" href="../favicon.ico" type="image/x-icon" sizes="16x16 32x32 48x48">
  <link rel="icon" href="../favicon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="../favicon.svg" sizes="180x180">
  <link rel="manifest" href="../assets/images/site.webmanifest">
  <link rel="preload" href="../assets/css/main.css" as="style">
  <link rel="preload" href="../assets/js/utils.js" as="script">
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <!-- Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    rel="stylesheet">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
  <link rel="stylesheet" href="../assets/css/calculator.css">
  <link rel="stylesheet" href="../assets/css/responsive.css">
  <link rel="stylesheet" href="../assets/css/footer.css">
  <!-- Open Graph Tags -->
  <meta property="og:title" content="Capital Gains Tax Calculator | Free Online Capital Gains Tax Estimator">
  <meta property="og:description"
    content="Calculate your capital gains tax liability for stocks, property, and other assets with our free online capital gains tax calculator.">
  <meta property="og:url" content="https://www.calculatorsuites.com/tax/free-capital-gains-calculator.html">
  <meta property="og:type" content="website">
  <meta property="og:image" content="https://www.calculatorsuites.com/assets/images/og-tax-calculators.jpg">
  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Capital Gains Tax Calculator | Free Online Capital Gains Tax Estimator">
  <meta name="twitter:description"
    content="Calculate your capital gains tax liability for stocks, property, and other assets with our free online capital gains tax calculator.">
  <meta name="twitter:image" content="https://www.calculatorsuites.com/assets/images/og-tax-calculators.jpg">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://www.calculatorsuites.com/tax/free-capital-gains-calculator.html">
  <!-- SoftwareApplication Schema -->
  
      <div class="content-section">
        <h2>Understanding Capital Gains Tax in India</h2>
        <p>Capital gains tax applies to profits from selling assets like stocks, mutual funds, real estate, and other investments. Our calculator helps you determine your tax liability based on holding period and asset type.</p>
        
        <h3>Types of Capital Gains</h3>
        <ul>
          <li><strong>Short-term Capital Gains (STCG):</strong> Assets held for less than 36 months (12 months for equity)</li>
          <li><strong>Long-term Capital Gains (LTCG):</strong> Assets held for more than 36 months (12 months for equity)</li>
        </ul>
        
        <h3>Tax Rates for 2024-25</h3>
        <ul>
          <li><strong>Equity STCG:</strong> 15% (plus cess and surcharge)</li>
          <li><strong>Equity LTCG:</strong> 10% on gains above ₹1 lakh (without indexation)</li>
          <li><strong>Non-equity STCG:</strong> As per income tax slab rates</li>
          <li><strong>Non-equity LTCG:</strong> 20% with indexation benefit</li>
        </ul>
        
        <h3>Exemptions and Deductions</h3>
        <p>Section 54, 54B, 54D, 54EC, and 54F provide various exemptions for capital gains. Reinvesting in specified assets can help reduce or eliminate capital gains tax liability.</p>
      </div>

      
      <div class="calculator-container">
        <div class="calculator-form">
          <h2>Capital Gains Tax Calculator</h2>
          <div class="form-group">
            <label for="purchasePrice">Purchase Price (₹)</label>
            <input type="number" id="purchasePrice" placeholder="Enter purchase price" min="0">
          </div>
          <div class="form-group">
            <label for="salePrice">Sale Price (₹)</label>
            <input type="number" id="salePrice" placeholder="Enter sale price" min="0">
          </div>
          <div class="form-group">
            <label for="assetType">Asset Type</label>
            <select id="assetType">
              <option value="equity">Equity Shares/Mutual Funds</option>
              <option value="property">Real Estate Property</option>
              <option value="gold">Gold/Precious Metals</option>
              <option value="bonds">Bonds/Debentures</option>
              <option value="other">Other Assets</option>
            </select>
          </div>
          <div class="form-group">
            <label for="holdingPeriod">Holding Period (months)</label>
            <input type="number" id="holdingPeriod" placeholder="Enter holding period" min="1">
          </div>
          <div class="form-group">
            <label for="indexationYear">Purchase Year (for indexation)</label>
            <input type="number" id="indexationYear" placeholder="e.g., 2020" min="2000" max="2024">
          </div>
          <button type="button" onclick="calculateCapitalGains()" class="calculate-btn">Calculate Capital Gains Tax</button>
          <div id="result" class="result-section"></div>
        </div>
      </div>
      
      <div class="content-section">
        <h2>Understanding Capital Gains Tax in India</h2>
        <p>Capital gains tax applies to profits from selling assets like stocks, mutual funds, real estate, and other investments. The tax rate depends on the type of asset and holding period.</p>
        
        <h3>Short-term vs Long-term Capital Gains</h3>
        <table class="tax-table">
          <tr>
            <th>Asset Type</th>
            <th>Short-term Period</th>
            <th>Long-term Period</th>
            <th>STCG Tax Rate</th>
            <th>LTCG Tax Rate</th>
          </tr>
          <tr>
            <td>Equity Shares/Mutual Funds</td>
            <td>≤ 12 months</td>
            <td>> 12 months</td>
            <td>15%</td>
            <td>10% (above ₹1 lakh)</td>
          </tr>
          <tr>
            <td>Real Estate</td>
            <td>≤ 24 months</td>
            <td>> 24 months</td>
            <td>As per slab</td>
            <td>20% (with indexation)</td>
          </tr>
          <tr>
            <td>Other Assets</td>
            <td>≤ 36 months</td>
            <td>> 36 months</td>
            <td>As per slab</td>
            <td>20% (with indexation)</td>
          </tr>
        </table>
        
        <h3>Capital Gains Tax Exemptions</h3>
        <ul>
          <li><strong>Section 54:</strong> Exemption on sale of residential property</li>
          <li><strong>Section 54EC:</strong> Investment in specified bonds</li>
          <li><strong>Section 54F:</strong> Investment in residential property</li>
          <li><strong>Section 80C:</strong> ELSS mutual fund investments</li>
        </ul>
        
        <h3>Indexation Benefit</h3>
        <p>For long-term capital gains on non-equity assets, indexation benefit allows you to adjust the purchase price for inflation, reducing your taxable gains. The Cost Inflation Index (CII) is notified annually by the government.</p>
      </div>
      
      
      <div class="calculator-container">
        <div class="calculator-form">
          <h2>Capital Gains Tax Calculator</h2>
          <div class="form-row">
            <div class="form-group">
              <label for="purchasePrice">Purchase Price (₹)</label>
              <input type="number" id="purchasePrice" placeholder="Enter purchase price" min="0" step="0.01">
            </div>
            <div class="form-group">
              <label for="salePrice">Sale Price (₹)</label>
              <input type="number" id="salePrice" placeholder="Enter sale price" min="0" step="0.01">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="assetType">Asset Type</label>
              <select id="assetType" onchange="updateHoldingPeriod()">
                <option value="equity">Equity Shares/Mutual Funds</option>
                <option value="property">Real Estate Property</option>
                <option value="gold">Gold/Precious Metals</option>
                <option value="bonds">Bonds/Debentures</option>
                <option value="other">Other Assets</option>
              </select>
            </div>
            <div class="form-group">
              <label for="holdingPeriod">Holding Period (months)</label>
              <input type="number" id="holdingPeriod" placeholder="Enter holding period" min="1">
              <small id="holdingHint">For equity: >12 months = Long-term</small>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="purchaseYear">Purchase Year (for indexation)</label>
              <input type="number" id="purchaseYear" placeholder="e.g., 2020" min="2000" max="2024">
            </div>
            <div class="form-group">
              <label for="expenses">Additional Expenses (₹)</label>
              <input type="number" id="expenses" placeholder="Brokerage, legal fees, etc." min="0" step="0.01">
            </div>
          </div>
          
          <button type="button" onclick="calculateCapitalGains()" class="calculate-btn">Calculate Capital Gains Tax</button>
          <button type="button" onclick="resetCalculator()" class="reset-btn">Reset</button>
          
          <div id="result" class="result-section"></div>
        </div>
      </div>
      
      <div class="content-section">
        <h2>Understanding Capital Gains Tax in India</h2>
        <p>Capital gains tax applies to profits from selling assets like stocks, mutual funds, real estate, and other investments. The tax rate depends on the type of asset and holding period. Our calculator helps you determine your exact tax liability.</p>
        
        <h3>Capital Gains Tax Rates (2024-25)</h3>
        <div class="tax-rates-grid">
          <div class="tax-rate-card">
            <h4>Equity Shares & Equity Mutual Funds</h4>
            <ul>
              <li><strong>Short-term (≤12 months):</strong> 15% + cess</li>
              <li><strong>Long-term (>12 months):</strong> 10% on gains above ₹1 lakh + cess</li>
            </ul>
          </div>
          
          <div class="tax-rate-card">
            <h4>Real Estate Property</h4>
            <ul>
              <li><strong>Short-term (≤24 months):</strong> As per income tax slab</li>
              <li><strong>Long-term (>24 months):</strong> 20% with indexation + cess</li>
            </ul>
          </div>
          
          <div class="tax-rate-card">
            <h4>Other Assets (Gold, Bonds, etc.)</h4>
            <ul>
              <li><strong>Short-term (≤36 months):</strong> As per income tax slab</li>
              <li><strong>Long-term (>36 months):</strong> 20% with indexation + cess</li>
            </ul>
          </div>
        </div>
        
        <h3>Capital Gains Tax Exemptions</h3>
        <ul>
          <li><strong>Section 54:</strong> Exemption on sale of residential house property</li>
          <li><strong>Section 54B:</strong> Exemption on sale of agricultural land</li>
          <li><strong>Section 54D:</strong> Exemption on compulsory acquisition of land/building</li>
          <li><strong>Section 54EC:</strong> Investment in specified bonds (₹50 lakh limit)</li>
          <li><strong>Section 54F:</strong> Investment in residential property (for non-residential assets)</li>
        </ul>
        
        <h3>Indexation Benefit Explained</h3>
        <p>For long-term capital gains on non-equity assets, indexation benefit allows you to adjust the purchase price for inflation using the Cost Inflation Index (CII). This reduces your taxable capital gains significantly.</p>
        
        <h4>Cost Inflation Index (CII) Values</h4>
        <div class="cii-table">
          <table>
            <tr><th>Financial Year</th><th>CII</th><th>Financial Year</th><th>CII</th></tr>
            <tr><td>2023-24</td><td>331</td><td>2018-19</td><td>280</td></tr>
            <tr><td>2022-23</td><td>317</td><td>2017-18</td><td>272</td></tr>
            <tr><td>2021-22</td><td>301</td><td>2016-17</td><td>264</td></tr>
            <tr><td>2020-21</td><td>301</td><td>2015-16</td><td>254</td></tr>
            <tr><td>2019-20</td><td>289</td><td>2014-15</td><td>240</td></tr>
          </table>
        </div>
        
        <h3>Tax Planning Strategies</h3>

      <div class="content-section">
        <h2>Capital Gains Planning Examples</h2>
        <p>Understanding capital gains through practical examples helps in better tax planning. Here are common scenarios and their tax implications.</p>
        
        <h3>Example 1: Equity Share Sale</h3>
        <div class="example-box">
          <p><strong>Scenario:</strong> Purchased shares for ₹1,00,000 in Jan 2022, sold for ₹1,50,000 in Mar 2024</p>
          <p><strong>Holding Period:</strong> 26 months (Long-term)</p>
          <p><strong>Capital Gain:</strong> ₹50,000</p>
          <p><strong>Tax:</strong> ₹0 (Exempt up to ₹1 lakh for equity LTCG)</p>
        </div>
        
        <h3>Example 2: Real Estate Sale</h3>
        <div class="example-box">
          <p><strong>Scenario:</strong> Purchased property for ₹50,00,000 in 2018, sold for ₹80,00,000 in 2024</p>
          <p><strong>Holding Period:</strong> 6 years (Long-term)</p>
          <p><strong>Indexed Cost:</strong> ₹50,00,000 × (331/280) = ₹59,10,714</p>
          <p><strong>Indexed Gain:</strong> ₹80,00,000 - ₹59,10,714 = ₹20,89,286</p>
          <p><strong>Tax:</strong> ₹20,89,286 × 20% = ₹4,17,857</p>
        </div>
        
        <h3>Tax Saving Strategies</h3>
        <ul>
          <li><strong>Timing of Sale:</strong> Hold assets for long-term to benefit from lower tax rates</li>
          <li><strong>Loss Harvesting:</strong> Offset gains with losses from other investments</li>
          <li><strong>Section 54 Exemption:</strong> Reinvest in residential property to save tax</li>
          <li><strong>Section 54EC Bonds:</strong> Invest in specified bonds within 6 months</li>
          <li><strong>Staggered Sales:</strong> Spread sales across financial years to manage tax brackets</li>
        </ul>
        
        <h3>Record Keeping Requirements</h3>
        <p>Maintain detailed records of all transactions including purchase receipts, sale deeds, improvement costs, and indexation calculations. Proper documentation is crucial for claiming exemptions and defending tax positions.</p>
        
        <h3>Recent Changes in Capital Gains Tax</h3>
        <p>Stay updated with recent changes in capital gains tax rules. The government periodically revises tax rates, exemption limits, and holding period requirements. Our calculator is regularly updated to reflect the latest tax provisions.</p>
        
        <h3>Professional Tax Planning</h3>
        <p>For high-value transactions or complex scenarios, consider consulting with tax professionals. They can help structure transactions optimally and ensure compliance with all applicable provisions.</p>
      </div>
        <ul>
          <li><strong>Timing of Sale:</strong> Plan asset sales to optimize between short-term and long-term rates</li>
          <li><strong>Loss Harvesting:</strong> Offset gains with losses from other investments</li>
          <li><strong>Exemption Planning:</strong> Utilize available exemptions under various sections</li>
          <li><strong>Staggered Sales:</strong> Spread sales across financial years to manage tax liability</li>
        </ul>
      </div>
      
      <script>
      function updateHoldingPeriod() {
        const assetType = document.getElementById('assetType').value;
        const hint = document.getElementById('holdingHint');
        
        switch(assetType) {
          case 'equity':
            hint.textContent = 'For equity: >12 months = Long-term';
            break;
          case 'property':
            hint.textContent = 'For property: >24 months = Long-term';
            break;
          default:
            hint.textContent = 'For other assets: >36 months = Long-term';
        }
      }
      
      function calculateCapitalGains() {
        const purchasePrice = parseFloat(document.getElementById('purchasePrice').value) || 0;
        const salePrice = parseFloat(document.getElementById('salePrice').value) || 0;
        const assetType = document.getElementById('assetType').value;
        const holdingPeriod = parseInt(document.getElementById('holdingPeriod').value) || 0;
        const purchaseYear = parseInt(document.getElementById('purchaseYear').value);
        const expenses = parseFloat(document.getElementById('expenses').value) || 0;
        
        if (!purchasePrice || !salePrice || !holdingPeriod) {
          document.getElementById('result').innerHTML = '<p class="error">Please fill all required fields (Purchase Price, Sale Price, Holding Period)</p>';
          return;
        }
        
        const grossGain = salePrice - purchasePrice - expenses;
        if (grossGain <= 0) {
          document.getElementById('result').innerHTML = '<p class="no-gain">No capital gains tax as there is no profit from this transaction.</p>';
          return;
        }
        
        let isLongTerm = false;
        let taxRate = 0;
        let exemptAmount = 0;
        
        // Determine if long-term based on asset type
        if (assetType === 'equity') {
          isLongTerm = holdingPeriod > 12;
          taxRate = isLongTerm ? 0.10 : 0.15;
          exemptAmount = isLongTerm ? 100000 : 0;
        } else if (assetType === 'property') {
          isLongTerm = holdingPeriod > 24;
          taxRate = isLongTerm ? 0.20 : 0.30; // Assuming 30% tax bracket
        } else {
          isLongTerm = holdingPeriod > 36;
          taxRate = isLongTerm ? 0.20 : 0.30; // Assuming 30% tax bracket
        }
        
        // Apply indexation for long-term non-equity assets
        let indexedCost = purchasePrice;
        let indexationBenefit = 0;
        if (isLongTerm && assetType !== 'equity' && purchaseYear) {
          const currentCII = 331; // 2023-24 CII
          const purchaseYearCII = getCII(purchaseYear);
          if (purchaseYearCII) {
            indexedCost = purchasePrice * (currentCII / purchaseYearCII);
            indexationBenefit = indexedCost - purchasePrice;
          }
        }
        
        const adjustedGain = Math.max(0, salePrice - indexedCost - expenses);
        const taxableGain = Math.max(0, adjustedGain - exemptAmount);
        const tax = taxableGain * taxRate;
        const cess = tax * 0.04; // 4% cess
        const totalTax = tax + cess;
        
        document.getElementById('result').innerHTML = `
          <h3>Capital Gains Tax Calculation Results</h3>
          <div class="result-grid">
            <div class="result-section">
              <h4>Transaction Details</h4>
              <div class="result-item">
                <label>Purchase Price:</label>
                <span>₹${purchasePrice.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Sale Price:</label>
                <span>₹${salePrice.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Additional Expenses:</label>
                <span>₹${expenses.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Gross Capital Gain:</label>
                <span>₹${grossGain.toLocaleString()}</span>
              </div>
            </div>
            
            <div class="result-section">
              <h4>Tax Calculation</h4>
              <div class="result-item">
                <label>Gain Type:</label>
                <span>${isLongTerm ? 'Long-term' : 'Short-term'} (${holdingPeriod} months)</span>
              </div>
              ${indexationBenefit > 0 ? `
              <div class="result-item">
                <label>Indexed Purchase Cost:</label>
                <span>₹${indexedCost.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Indexation Benefit:</label>
                <span>₹${indexationBenefit.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Indexed Capital Gain:</label>
                <span>₹${adjustedGain.toLocaleString()}</span>
              </div>
              ` : ''}
              ${exemptAmount > 0 ? `
              <div class="result-item">
                <label>Exempt Amount:</label>
                <span>₹${exemptAmount.toLocaleString()}</span>
              </div>
              ` : ''}
              <div class="result-item">
                <label>Taxable Capital Gain:</label>
                <span>₹${taxableGain.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Tax Rate:</label>
                <span>${(taxRate * 100)}%</span>
              </div>
              <div class="result-item">
                <label>Capital Gains Tax:</label>
                <span>₹${tax.toLocaleString()}</span>
              </div>
              <div class="result-item">
                <label>Health & Education Cess (4%):</label>
                <span>₹${cess.toLocaleString()}</span>
              </div>
              <div class="result-item highlight">
                <label>Total Tax Liability:</label>
                <span>₹${totalTax.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div class="tax-summary">
            <h4>Summary</h4>
            <p><strong>Net Proceeds after Tax:</strong> ₹${(salePrice - expenses - totalTax).toLocaleString()}</p>
            <p><strong>Effective Tax Rate:</strong> ${((totalTax / grossGain) * 100).toFixed(2)}% of gross gains</p>
          </div>
        `;
      }
      
      function resetCalculator() {
        document.getElementById('purchasePrice').value = '';
        document.getElementById('salePrice').value = '';
        document.getElementById('holdingPeriod').value = '';
        document.getElementById('purchaseYear').value = '';
        document.getElementById('expenses').value = '';
        document.getElementById('assetType').value = 'equity';
        document.getElementById('result').innerHTML = '';
        updateHoldingPeriod();
      }
      
      function getCII(year) {
        const ciiData = {
          2023: 331, 2022: 317, 2021: 301, 2020: 301, 2019: 289,
          2018: 280, 2017: 272, 2016: 264, 2015: 254, 2014: 240,
          2013: 220, 2012: 200, 2011: 184, 2010: 167, 2009: 148,
          2008: 137, 2007: 129, 2006: 122, 2005: 117, 2004: 113
        };
        return ciiData[year] || null;
      }
      
      // Initialize
      updateHoldingPeriod();
      </script>

      <script>
      function calculateCapitalGains() {
        const purchasePrice = parseFloat(document.getElementById('purchasePrice').value);
        const salePrice = parseFloat(document.getElementById('salePrice').value);
        const assetType = document.getElementById('assetType').value;
        const holdingPeriod = parseInt(document.getElementById('holdingPeriod').value);
        const indexationYear = parseInt(document.getElementById('indexationYear').value);
        
        if (!purchasePrice || !salePrice || !holdingPeriod) {
          document.getElementById('result').innerHTML = '<p class="error">Please fill all required fields</p>';
          return;
        }
        
        const capitalGain = salePrice - purchasePrice;
        if (capitalGain <= 0) {
          document.getElementById('result').innerHTML = '<p class="no-gain">No capital gains tax as there is no profit from this transaction.</p>';
          return;
        }
        
        let isLongTerm = false;
        let taxRate = 0;
        let exemptAmount = 0;
        
        // Determine if long-term based on asset type
        if (assetType === 'equity') {
          isLongTerm = holdingPeriod > 12;
          taxRate = isLongTerm ? 0.10 : 0.15;
          exemptAmount = isLongTerm ? 100000 : 0;
        } else if (assetType === 'property') {
          isLongTerm = holdingPeriod > 24;
          taxRate = isLongTerm ? 0.20 : 0.30;
        } else {
          isLongTerm = holdingPeriod > 36;
          taxRate = isLongTerm ? 0.20 : 0.30;
        }
        
        // Apply indexation for long-term non-equity assets
        let indexedCost = purchasePrice;
        if (isLongTerm && assetType !== 'equity' && indexationYear) {
          const currentCII = 331; // 2023-24 CII
          const purchaseYearCII = getCII(indexationYear);
          if (purchaseYearCII) {
            indexedCost = purchasePrice * (currentCII / purchaseYearCII);
          }
        }
        
        const adjustedGain = salePrice - indexedCost;
        const taxableGain = Math.max(0, adjustedGain - exemptAmount);
        const tax = taxableGain * taxRate;
        
        document.getElementById('result').innerHTML = `
          <h3>Capital Gains Tax Calculation Results</h3>
          <div class="result-grid">
            <div class="result-item">
              <label>Purchase Price:</label>
              <span>₹${purchasePrice.toLocaleString()}</span>
            </div>
            <div class="result-item">
              <label>Sale Price:</label>
              <span>₹${salePrice.toLocaleString()}</span>
            </div>
            <div class="result-item">
              <label>Capital Gain:</label>
              <span>₹${capitalGain.toLocaleString()}</span>
            </div>
            <div class="result-item">
              <label>Gain Type:</label>
              <span>${isLongTerm ? 'Long-term' : 'Short-term'}</span>
            </div>
            ${indexedCost !== purchasePrice ? `
            <div class="result-item">
              <label>Indexed Cost:</label>
              <span>₹${indexedCost.toLocaleString()}</span>
            </div>
            <div class="result-item">
              <label>Indexed Gain:</label>
              <span>₹${adjustedGain.toLocaleString()}</span>
            </div>
            ` : ''}
            ${exemptAmount > 0 ? `
            <div class="result-item">
              <label>Exempt Amount:</label>
              <span>₹${exemptAmount.toLocaleString()}</span>
            </div>
            ` : ''}
            <div class="result-item">
              <label>Taxable Gain:</label>
              <span>₹${taxableGain.toLocaleString()}</span>
            </div>
            <div class="result-item">
              <label>Tax Rate:</label>
              <span>${(taxRate * 100)}%</span>
            </div>
            <div class="result-item highlight">
              <label>Capital Gains Tax:</label>
              <span>₹${tax.toLocaleString()}</span>
            </div>
          </div>
        `;
      }
      
      function getCII(year) {
        const ciiData = {
          2023: 331, 2022: 317, 2021: 301, 2020: 301, 2019: 289,
          2018: 280, 2017: 272, 2016: 264, 2015: 254, 2014: 240,
          2013: 220, 2012: 200, 2011: 184, 2010: 167, 2009: 148
        };
        return ciiData[year] || null;
      }
      </script>

      <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Capital Gains Calculator | Calculator Suites",
    "applicationCategory": "FinanceTool",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Calculate capital gains tax on your investments. Supports both short-term and long-term capital gains calculations with accurate tax rates."
  }
  </script>
  <!-- BreadcrumbList Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.calculatorsuites.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Tax Calculators",
        "item": "https://www.calculatorsuites.com/tax/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "Capital Gains Calculator",
        "item": "https://www.calculatorsuites.com/tax/free-capital-gains-calculator.html"
      }
    ]
  }
  </script>

    <style>
      .site-header {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      flex-direction: column;
    }

    .logo-link {
      text-decoration: none;
    }

    .logo-text {
      font-family: var(--font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--primary-color), #f72585);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      transition: all 0.2s ease;
      padding: 0.5rem;
    }

    .nav-title {
      font-weight: 600;
      font-size: 0.95rem;
      color: var(--neutral-800);
      line-height: 1.2;
    }

    .nav-subtitle {
      font-weight: 400;
      font-size: 0.8rem;
      color: var(--neutral-600);
      line-height: 1.2;
      margin-top: 0.1rem;
    }

    .nav-item:hover .nav-title {
      color: var(--primary-color);
    }

    .nav-item:hover .nav-subtitle {
      color: var(--primary-color);
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-title {
        font-size: 1rem;
      }
      
      .nav-subtitle {
        font-size: 0.85rem;
      }
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      color: var(--neutral-800);
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      padding: 0.5rem 0;
      transition: color 0.2s ease;
      white-space: nowrap;
    }

    .nav-item:hover {
      color: var(--primary-color);
      text-decoration: none;
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }

      .nav-container {
        padding: 1rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-item {
        font-size: 1rem;
      }
    }
      
      .nav-menu {
        display: flex;
        gap: 30px;
        align-items: center;
      }
      
      .nav-item {
        text-decoration: none;
        color: #374151;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
        min-width: 80px;
      }
      
      .nav-item:hover {
        background-color: #f3f4f6;
        color: #6366f1;
      }
      
      .nav-title {
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
      }
      
      .nav-subtitle {
        font-size: 11px;
        color: #6b7280;
        line-height: 1.1;
        margin-top: 1px;
      }
      
      .nav-item:hover .nav-subtitle {
        color: #6366f1;
      }
      
      @media (max-width: 768px) {
        .nav-container {
          padding: 0 15px;
          height: 60px;
        }
        
        .nav-menu {
          gap: 15px;
        }
        
        .nav-item {
          padding: 6px 8px;
          min-width: 60px;
        }
        
        .nav-title {
          font-size: 12px;
        }
        
        .nav-subtitle {
          font-size: 10px;
        }
        
        .logo-text {
          font-size: 16px;
        }
      }
      
      @media (max-width: 480px) {
        .nav-menu {
          gap: 10px;
        }
        
        .nav-item {
          padding: 4px 6px;
          min-width: 50px;
        }
        
        .nav-title {
          font-size: 11px;
        }
        
        .nav-subtitle {
          display: none;
        }
      }
    </style>
</head>
<body>
  <!-- Header -->
        <header class="site-header">
    <nav class="main-navigation">
      <div class="nav-container">
        <div class="logo">
          <a href="/" class="logo-link">
            <span class="logo-text">Calculator</span>
            <span class="logo-text">Suites</span>
          </a>
        </div>
        <div class="nav-menu">
          <a href="/tax/" class="nav-item">
            <span class="nav-title">Tax</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/discount/" class="nav-item">
            <span class="nav-title">Discount</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/investment/" class="nav-item">
            <span class="nav-title">Investment</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/loan/" class="nav-item">
            <span class="nav-title">Loan</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/health/" class="nav-item">
            <span class="nav-title">Health</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/indian/" class="nav-item">
            <span class="nav-title">Indian</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/blog/" class="nav-item">
            <span class="nav-title">Blog</span>
          </a>
        </div>
        <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>
      </div>
    </nav>
  </header>
  <main class="main-content">
    <div class="container">
      <div class="grid">
        <div class="grid-col-lg-8">
          <!-- Calculator Introduction -->
          <div class="content-section">
            <h1>Free Capital Gains Tax Calculator</h1>
            <p class="intro-text">Calculate your capital gains tax liability with our free online calculator. Estimate
              taxes on profits from selling stocks, property, mutual funds, and other assets with our easy-to-use tool.
            </p>
          </div>
          <!-- Calculator Container -->
          <div class="calculator-container" data-calculator-type="tax" id="capital-gains-calculator">
            <h2>Capital Gains Tax Calculator</h2>
            <form id="capital-gains-calculator-form">
              <div class="form-group">
                <label for="asset-type">Asset Type:</label>
                <select id="asset-type" name="asset-type" required>
                  <option value="equity">Equity Shares/Mutual Funds</option>
                  <option value="property">Real Estate/Property</option>
                  <option value="other">Other Assets</option>
                </select>
              </div>
              <div class="form-group">
                <label for="holding-period">Holding Period:</label>
                <select id="holding-period" name="holding-period" required>
                  <option value="short">Short Term</option>
                  <option value="long">Long Term</option>
                </select>
              </div>
              <div class="form-group">
                <label for="purchase-price">Purchase Price (₹):</label>
                <input type="number" id="purchase-price" name="purchase-price" min="0" step="1000" required>
              </div>
              <div class="form-group">
                <label for="selling-price">Selling Price (₹):</label>
                <input type="number" id="selling-price" name="selling-price" min="0" step="1000" required>
              </div>
              <div class="form-group" id="purchase-date-group">
                <label for="purchase-date">Purchase Date:</label>
                <input type="date" id="purchase-date" name="purchase-date">
              </div>
              <div class="form-group" id="selling-date-group">
                <label for="selling-date">Selling Date:</label>
                <input type="date" id="selling-date" name="selling-date">
              </div>
              <div class="form-group" id="expenses-group">
                <label for="expenses">Expenses/Fees (?):</label>
                <input type="number" id="expenses" name="expenses" min="0" step="100">
              </div>
              <div class="form-group" id="income-slab-group">
                <label for="income-slab">Income Tax Slab:</label>
                <select id="income-slab" name="income-slab">
                  <option value="0.05">5%</option>
                  <option value="0.1">10%</option>
                  <option value="0.15">15%</option>
                  <option value="0.2">20%</option>
                  <option value="0.3" selected>30%</option>
                </select>
              </div>
              <button type="submit" class="calculate-btn">Calculate
