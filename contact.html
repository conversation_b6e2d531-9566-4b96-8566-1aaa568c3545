﻿<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-6BNPSB8DSK');
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="index, follow">
  <title>Contact CalculatorSuites - Free Calculator Support & Help</title>
  <meta name="description"
    content="Get in touch with the CalculatorSuites team for support, feedback, or partnership inquiries. We're here to help with questions about our free online calculators and financial tools.">

  <!-- Favicon -->
  <link rel="icon" href="/favicon.svg" type="image/svg+xml">
  <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16 32x32 48x48">
  <link rel="apple-touch-icon" href="/favicon.svg" sizes="180x180">
  <link rel="manifest" href="/assets/images/site.webmanifest">
  <link rel="preload" href="assets/css/main.css" as="style">
  <link rel="preload" href="assets/js/utils.js" as="script">
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <!-- Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    rel="stylesheet">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="assets/css/calculator.css">
  <link rel="stylesheet" href="assets/css/responsive.css">
  <link rel="stylesheet" href="assets/css/footer.css">
  <!-- Open Graph Tags -->
  <meta property="og:title" content="Contact Us | CalculatorSuites">
  <meta property="og:description"
    content="Get in touch with the CalculatorSuites team. We welcome your feedback, questions, and suggestions about our free online calculators.">
  <meta property="og:url" content="https://www.calculatorsuites.com/contact.html">
  <meta property="og:type" content="website">
  <meta property="og:image" content="https://www.calculatorsuites.com/assets/images/og-image.jpg">
  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Contact Us | CalculatorSuites">
  <meta name="twitter:description"
    content="Get in touch with the CalculatorSuites team. We welcome your feedback, questions, and suggestions about our free online calculators.">
  <meta name="twitter:image" content="https://www.calculatorsuites.com/assets/images/og-image.jpg">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://www.calculatorsuites.com/contact.html">

  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact CalculatorSuites",
    "description": "Get in touch with CalculatorSuites for support and inquiries",
    "url": "https://www.calculatorsuites.com/contact.html",
    "mainEntity": {
      "@type": "Organization",
      "name": "CalculatorSuites",
      "url": "https://www.calculatorsuites.com",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "Customer Service",
        "email": "<EMAIL>"
      }
    }
  }
  </script>

    <style>
      .site-header {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      flex-direction: column;
    }

    .logo-link {
      text-decoration: none;
    }

    .logo-text {
      font-family: var(--font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--primary-color), #f72585);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      transition: all 0.2s ease;
      padding: 0.5rem;
    }

    .nav-title {
      font-weight: 600;
      font-size: 0.95rem;
      color: var(--neutral-800);
      line-height: 1.2;
    }

    .nav-subtitle {
      font-weight: 400;
      font-size: 0.8rem;
      color: var(--neutral-600);
      line-height: 1.2;
      margin-top: 0.1rem;
    }

    .nav-item:hover .nav-title {
      color: var(--primary-color);
    }

    .nav-item:hover .nav-subtitle {
      color: var(--primary-color);
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-title {
        font-size: 1rem;
      }
      
      .nav-subtitle {
        font-size: 0.85rem;
      }
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      color: var(--neutral-800);
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      padding: 0.5rem 0;
      transition: color 0.2s ease;
      white-space: nowrap;
    }

    .nav-item:hover {
      color: var(--primary-color);
      text-decoration: none;
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }

      .nav-container {
        padding: 1rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-item {
        font-size: 1rem;
      }
    }
      
      .nav-menu {
        display: flex;
        gap: 30px;
        align-items: center;
      }
      
      .nav-item {
        text-decoration: none;
        color: #374151;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
        min-width: 80px;
      }
      
      .nav-item:hover {
        background-color: #f3f4f6;
        color: #6366f1;
      }
      
      .nav-title {
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
      }
      
      .nav-subtitle {
        font-size: 11px;
        color: #6b7280;
        line-height: 1.1;
        margin-top: 1px;
      }
      
      .nav-item:hover .nav-subtitle {
        color: #6366f1;
      }
      
      @media (max-width: 768px) {
        .nav-container {
          padding: 0 15px;
          height: 60px;
        }
        
        .nav-menu {
          gap: 15px;
        }
        
        .nav-item {
          padding: 6px 8px;
          min-width: 60px;
        }
        
        .nav-title {
          font-size: 12px;
        }
        
        .nav-subtitle {
          font-size: 10px;
        }
        
        .logo-text {
          font-size: 16px;
        }
      }
      
      @media (max-width: 480px) {
        .nav-menu {
          gap: 10px;
        }
        
        .nav-item {
          padding: 4px 6px;
          min-width: 50px;
        }
        
        .nav-title {
          font-size: 11px;
        }
        
        .nav-subtitle {
          display: none;
        }
      }
    </style>
</head>

<body>
  <!-- Header -->
        <header class="site-header">
    <nav class="main-navigation">
      <div class="nav-container">
        <div class="logo">
          <a href="/" class="logo-link">
            <span class="logo-text">Calculator</span>
            <span class="logo-text">Suites</span>
          </a>
        </div>
        <div class="nav-menu">
          <a href="/tax/" class="nav-item">
            <span class="nav-title">Tax</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/discount/" class="nav-item">
            <span class="nav-title">Discount</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/investment/" class="nav-item">
            <span class="nav-title">Investment</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/loan/" class="nav-item">
            <span class="nav-title">Loan</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/health/" class="nav-item">
            <span class="nav-title">Health</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/indian/" class="nav-item">
            <span class="nav-title">Indian</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/blog/" class="nav-item">
            <span class="nav-title">Blog</span>
          </a>
        </div>
        <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>
      </div>
    </nav>
  </header>
  <!-- Main Content -->
  <main class="main-content">
    <div class="container">
      <div class="grid">
        <div class="grid-col-lg-8">
          <!-- Contact Introduction -->
          <div class="content-section">
            <h1>Contact Us</h1>
            <p class="intro-text">Have questions, feedback, or suggestions about our calculators? We'd love to hear from
              you! Send us an email and we'll get back to you as soon as possible.</p>
          </div>
          <!-- Contact Information -->
          <div class="content-section">
            <h2>Get in Touch</h2>
            <div class="contact-info">
              <div class="contact-item">
                <h3>Email</h3>
                <p>For all inquiries: <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
              </div>
              <div class="contact-item">
                <h3>Response Time</h3>
                <p>We aim to respond to all inquiries within 1-2 business days.</p>
              </div>
            </div>
          </div>
          <!-- FAQ Section -->
          <div class="content-section">
            <h2>Frequently Asked Questions</h2>
            <div class="faq-item">
              <h3>Are your calculators free to use?</h3>
              <p>Yes, all calculators on our website are completely free to use. There are no hidden charges or premium
                features that require payment.</p>
            </div>
            <div class="faq-item">
              <h3>Can I suggest a new calculator?</h3>
              <p>Absolutely! We're always looking to expand our collection of calculators. Please email us at <a
                  href="mailto:<EMAIL>"><EMAIL></a> to share your ideas.</p>
            </div>
            <div class="faq-item">
              <h3>How accurate are your calculators?</h3>
              <p>We strive to make our calculators as accurate as possible by using standard formulas and methodologies.
                However, they should be used for informational purposes only and not as a substitute for professional
                advice.</p>
            </div>
            <div class="faq-item">
              <h3>Can I embed your calculators on my website?</h3>
              <p>Currently, we don't offer embedding options for our calculators. However, you're welcome to link to our
                calculator pages from your website.</p>
            </div>
          </div>

          <!-- What We Can Help With -->
          <div class="content-section">
            <h2>What We Can Help With</h2>
            <div class="help-categories">
              <div class="help-item">
                <h3>Calculator Support</h3>
                <p>Having trouble using one of our calculators? Need help understanding the results? We're here to guide
                  you through any calculator-related questions and ensure you get accurate results for your financial,
                  health, or business calculations.</p>
              </div>
              <div class="help-item">
                <h3>Feature Requests</h3>
                <p>Missing a calculator that would be helpful for your needs? We actively develop new calculators based
                  on user feedback. Share your ideas for new financial tools, health calculators, or business utilities
                  that would benefit our community.</p>
              </div>
              <div class="help-item">
                <h3>Technical Issues</h3>
                <p>Experiencing problems with our website, mobile compatibility, or calculator functionality? Report any
                  bugs, loading issues, or technical difficulties you encounter. We continuously improve our platform
                  based on user feedback.</p>
              </div>
              <div class="help-item">
                <h3>Business Partnerships</h3>
                <p>Interested in partnering with CalculatorSuites? Whether you're looking to integrate our tools,
                  explore collaboration opportunities, or discuss business relationships, we welcome partnership
                  inquiries from relevant organizations.</p>
              </div>
              <div class="help-item">
                <h3>Educational Use</h3>
                <p>Teachers, students, and educational institutions can contact us about using our calculators for
                  educational purposes. We're happy to provide guidance on incorporating our tools into curriculum or
                  educational materials.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-col-lg-4">
          <!-- Sidebar -->
          <aside class="sidebar">
            <!-- Popular Calculators -->
            <div class="sidebar-section">
              <h3>Popular Calculators</h3>
              <ul class="related-calculators">
                <li><a href="tax/free-gst-calculator.html">GST Calculator</a></li>
                <li><a href="investment/free-sip-calculator.html">SIP Calculator</a></li>
                <li><a href="loan/free-emi-calculator.html">EMI Calculator</a></li>
                <li><a href="health/free-bmi-calculator.html">BMI Calculator</a></li>
                <li><a href="discount/free-percentage.html">Discount Calculator</a></li>
              </ul>
            </div>
            <!-- Quick Links -->
            <div class="sidebar-section">
              <h3>Quick Links</h3>
              <ul class="quick-tips">
                <li><a href="tax/">Tax Calculators</a></li>
                <li><a href="discount/">Discount Calculators</a></li>
                <li><a href="investment/">Investment Calculators</a></li>
                <li><a href="loan/">Loan Calculators</a></li>
                <li><a href="health/">Health Calculators</a></li>
              </ul>
            </div>
          </aside>
        </div>
      </div>
    </div>
  </main>
  <!-- Scripts -->
  <script src="assets/js/utils.js" defer></script>
  <script src="assets/js/main.js" defer></script>
  <script src="assets/js/footer-loader.js" defer></script>
</body>

</html>