﻿<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-6BNPSB8DSK');
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Not Found | Niche Calculators</title>
  <meta name="robots" content="noindex, follow">
  <!-- Favicon -->
  <link rel="icon" href="favicon.svg" type="image/svg+xml">
  <link rel="icon" href="favicon.ico" type="image/x-icon" sizes="16x16 32x32 48x48">
  <link rel="apple-touch-icon" href="favicon.svg" sizes="180x180">
  <link rel="manifest" href="assets/images/site.webmanifest">
  <!-- Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    rel="stylesheet">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="assets/css/responsive.css">
  <link rel="stylesheet" href="assets/css/footer.css">
  <style>
    .error-container {
      text-align: center;
      padding: 5rem 1rem;
      max-width: 600px;
      margin: 0 auto;
    }
    .error-code {
      font-size: 8rem;
      font-weight: 700;
      color: var(--primary-color);
      line-height: 1;
      margin-bottom: 1rem;
    }
    .error-message {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
    }
    .error-description {
      margin-bottom: 2rem;
      color: var(--neutral-600);
    }
    .back-home-btn {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 0.25rem;
      font-weight: 500;
      text-decoration: none;
      transition: background-color 0.2s ease;
    }
    .back-home-btn:hover {
      background-color: var(--primary-dark);
      text-decoration: none;
    }
    .calculator-suggestions {
      margin-top: 3rem;
      text-align: left;
    }
    .calculator-suggestions h3 {
      margin-bottom: 1rem;
      text-align: center;
    }
    .suggestions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="site-header">
    <div class="container">
      <div class="nav-container">
        <a href="/" class="logo">
          <span class="logo-text">Calculator Suites</span>
        </a>
        <button class="mobile-menu-toggle" aria-label="Toggle menu">
          <span class="hamburger-icon"></span>
        </button>
        <ul class="nav-menu">
          <li class="nav-item has-dropdown">
            <a href="tax/" class="nav-link">Tax Calculators</a>
            <ul class="dropdown-menu">
              <li><a href="tax/free-gst-calculator.html">GST Calculator</a></li>
              <li><a href="tax/free-income-tax.html">Income Tax Calculator</a></li>
              <li><a href="tax/free-tax-comparison.html">Tax Comparison Tool</a></li>
            </ul>
          </li>
          <li class="nav-item has-dropdown">
            <a href="discount/" class="nav-link">Discount Calculators</a>
            <ul class="dropdown-menu">
              <li><a href="discount/free-percentage.html">Percentage Discount</a></li>
              <li><a href="discount/free-amount-based.html">Amount-based Discount</a></li>
              <li><a href="discount/free-bulk-discount.html">Bulk Discount</a></li>
            </ul>
          </li>
          <li class="nav-item has-dropdown">
            <a href="investment/" class="nav-link">Investment Calculators</a>
            <ul class="dropdown-menu">
              <li><a href="investment/free-sip-calculator.html">SIP Calculator</a></li>
              <li><a href="investment/free-compound-interest.html">Compound Interest</a></li>
              <li><a href="investment/free-lump-sum.html">Lump Sum Investment</a></li>
              <li><a href="investment/free-goal-calculator.html">Investment Goal</a></li>
            </ul>
          </li>
          <li class="nav-item has-dropdown">
            <a href="loan/" class="nav-link">Loan Calculators</a>
            <ul class="dropdown-menu">
              <li><a href="loan/free-emi-calculator.html">EMI Calculator</a></li>
              <li><a href="loan/free-affordability.html">Loan Affordability</a></li>
              <li><a href="loan/free-comparison.html">Loan Comparison</a></li>
              <li><a href="loan/free-amortization.html">Amortization Schedule</a></li>
            </ul>
          </li>
          <li class="nav-item has-dropdown">
            <a href="health/" class="nav-link">Health Calculators</a>
            <ul class="dropdown-menu">
              <li><a href="health/free-bmi-calculator.html">BMI Calculator</a></li>
              <li><a href="health/free-calorie-calculator.html">Calorie Calculator</a></li>
              <li><a href="health/free-pregnancy.html">Pregnancy Due Date</a></li>
              <li><a href="health/free-body-fat.html">Body Fat Percentage</a></li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </header>
  <!-- Main Content -->
  <main class="main-content">
    <div class="container">
      <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-message">Page Not Found</h1>
        <p class="error-description">The page you are looking for might have been removed, had its name changed, or is
          temporarily unavailable.</p>
        <a href="/" class="back-home-btn">Back to Home</a>
        <div class="calculator-suggestions">
          <h3>Try Our Popular Calculators</h3>
          <div class="suggestions-grid">
            <a href="tax/free-gst-calculator.html" class="suggestion-link">GST Calculator</a>
            <a href="investment/free-sip-calculator.html" class="suggestion-link">SIP Calculator</a>
            <a href="loan/free-emi-calculator.html" class="suggestion-link">EMI Calculator</a>
            <a href="health/free-bmi-calculator.html" class="suggestion-link">BMI Calculator</a>
            <a href="discount/free-percentage.html" class="suggestion-link">Discount Calculator</a>
            <a href="investment/free-compound-interest.html" class="suggestion-link">Compound Interest</a>
          </div>
        </div>
      </div>
  </main>
  <!-- Scripts -->
  <script src="assets/js/utils.js" defer></script>
  <script src="assets/js/main.js" defer></script>\n</body>
</html>
