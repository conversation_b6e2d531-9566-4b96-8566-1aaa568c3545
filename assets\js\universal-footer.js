/**
 * Universal Footer Loader
 * Ensures all pages have a consistent footer
 */

(function () {
  "use strict";

  // Check if footer already exists
  if (document.querySelector(".site-footer")) {
    console.log("Footer already exists on page");
    return;
  }

  /**
   * Determine the correct path to footer.html based on current location
   */
  function getFooterPath() {
    const currentPath = window.location.pathname;

    // For root directory
    if (currentPath === "/" || currentPath.endsWith("index.html")) {
      return "./footer.html";
    }

    // For subdirectories (blog, tax, loan, etc.)
    if (
      currentPath.includes("/blog/") ||
      currentPath.includes("/tax/") ||
      currentPath.includes("/loan/") ||
      currentPath.includes("/investment/") ||
      currentPath.includes("/health/") ||
      currentPath.includes("/discount/")
    ) {
      return "../footer.html";
    }

    // Default fallback
    return "./footer.html";
  }

  /**
   * Create fallback footer HTML
   */
  function createFallbackFooter() {
    const currentPath = window.location.pathname;
    const isSubdirectory =
      currentPath.includes("/blog/") ||
      currentPath.includes("/tax/") ||
      currentPath.includes("/loan/") ||
      currentPath.includes("/investment/") ||
      currentPath.includes("/health/") ||
      currentPath.includes("/discount/");

    const pathPrefix = isSubdirectory ? "../" : "";

    return `
      <footer class="site-footer">
        <div class="container">
          <div class="footer-grid">
            <div class="footer-column">
              <h4>Calculator Suites</h4>
              <p>Free online calculators for all your financial, tax, health, and investment calculation needs.</p>
              <div class="footer-social">
                <a href="https://github.com/rajesh521/Calculatorsuites" target="_blank" rel="noopener" aria-label="GitHub">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </a>
              </div>
            </div>
            
            <div class="footer-column">
              <h4>Calculator Categories</h4>
              <ul class="footer-links">
                <li><a href="${pathPrefix}tax/">Tax Calculators</a></li>
                <li><a href="${pathPrefix}discount/">Discount Calculators</a></li>
                <li><a href="${pathPrefix}investment/">Investment Calculators</a></li>
                <li><a href="${pathPrefix}loan/">Loan Calculators</a></li>
                <li><a href="${pathPrefix}health/">Health Calculators</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h4>Popular Tools</h4>
              <ul class="footer-links">
                <li><a href="${pathPrefix}tax/free-gst-calculator.html">GST Calculator</a></li>
                <li><a href="${pathPrefix}investment/free-sip-calculator.html">SIP Calculator</a></li>
                <li><a href="${pathPrefix}loan/free-emi-calculator.html">EMI Calculator</a></li>
                <li><a href="${pathPrefix}health/free-bmi-calculator.html">BMI Calculator</a></li>
                <li><a href="${pathPrefix}discount/free-percentage-calculator.html">Percentage Calculator</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h4>Resources & Guides</h4>
              <ul class="footer-links">
                <li><a href="${pathPrefix}blog/">Financial Planning Blog</a></li>
                <li><a href="${pathPrefix}blog/calculator-selection-guide.html">Calculator Selection Guide</a></li>
                <li><a href="${pathPrefix}blog/tax-planning-strategies-2024.html">Tax Planning Tips</a></li>
                <li><a href="${pathPrefix}blog/complete-sip-investment-guide.html">Investment Guides</a></li>
                <li><a href="${pathPrefix}blog/inexpensive-calculators-guide.html">Free Calculator Guide</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h4>Support & Info</h4>
              <ul class="footer-links">
                <li><a href="${pathPrefix}contact.html">Contact Us</a></li>
                <li><a href="${pathPrefix}privacy.html">Privacy Policy</a></li>
                <li><a href="${pathPrefix}how-it-works.html">How It Works</a></li>
                <li><a href="${pathPrefix}faq.html">FAQ</a></li>
              </ul>
            </div>
          </div>
          
          <div class="footer-bottom">
            <div class="footer-bottom-content">
              <p>&copy; 2025 CalculatorSuites. All rights reserved.</p>
              <div class="footer-bottom-links">
                <a href="${pathPrefix}privacy.html">Privacy</a>
                <span>•</span>
                <a href="${pathPrefix}terms.html">Terms</a>
                <span>•</span>
                <a href="${pathPrefix}sitemap.html">Sitemap</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    `;
  }

  /**
   * Load footer from external file or create fallback
   */
  function loadFooter() {
    const footerPath = getFooterPath();

    fetch(footerPath)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then((data) => {
        // Insert footer at the end of body
        document.body.insertAdjacentHTML("beforeend", data);

        // Make footer visible
        const footer = document.querySelector(".site-footer");
        if (footer) {
          footer.style.opacity = "1";
          footer.style.transition = "opacity 0.3s ease-in-out";
          console.log("Footer loaded successfully from:", footerPath);
        }
      })
      .catch((error) => {
        console.warn("Failed to load footer from external file:", error);
        console.log("Creating fallback footer");

        // Create fallback footer
        const fallbackFooter = createFallbackFooter();
        document.body.insertAdjacentHTML("beforeend", fallbackFooter);

        // Make fallback footer visible
        const footer = document.querySelector(".site-footer");
        if (footer) {
          footer.style.opacity = "1";
          footer.style.transition = "opacity 0.3s ease-in-out";
          console.log("Fallback footer created successfully");
        }
      });
  }

  // Load footer when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadFooter);
  } else {
    loadFooter();
  }
})();
