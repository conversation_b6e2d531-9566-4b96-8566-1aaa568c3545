# Simple local server starter for CalculatorSuites
Write-Host "Starting local development server for CalculatorSuites..." -ForegroundColor Green

# Try different ports
$ports = @(3000, 4000, 5000, 8080, 8888)

foreach ($port in $ports) {
    try {
        Write-Host "Trying port $port..." -ForegroundColor Yellow
        
        # Start Python HTTP server
        Start-Process python -ArgumentList "-m", "http.server", "$port" -WorkingDirectory $PWD
        
        Write-Host "✅ Server started successfully on port $port!" -ForegroundColor Green
        Write-Host "🌐 Open your browser and go to: http://localhost:$port" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📌 To view the new Indian calculators:" -ForegroundColor White
        Write-Host "   • Main site: http://localhost:$port/index.html" -ForegroundColor Gray
        Write-Host "   • Indian calculators: http://localhost:$port/indian/" -ForegroundColor Gray
        Write-Host "   • EPF Calculator: http://localhost:$port/indian/epf-calculator.html" -ForegroundColor Gray
        Write-Host "   • PPF Calculator: http://localhost:$port/indian/ppf-calculator.html" -ForegroundColor Gray
        Write-Host "   • ELSS Calculator: http://localhost:$port/indian/elss-calculator.html" -ForegroundColor Gray
        Write-Host "   • FY Calculator: http://localhost:$port/indian/fy-calculator.html" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Red
        
        # Wait for user input
        Read-Host "Press Enter to continue or Ctrl+C to stop"
        break
    }
    catch {
        Write-Host "Port $port is busy, trying next..." -ForegroundColor Yellow
        continue
    }
}

if ($port -eq $ports[-1]) {
    Write-Host "❌ All ports are busy. Please close other applications or restart your computer." -ForegroundColor Red
}
