/**
 * Main JavaScript file for CalculatorSuites
 */

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize share functionality
  initShareFunctionality();

  // Initialize scroll to top button
  initScrollToTop();
  // Load footer only if it doesn't already exist
  if (!document.querySelector(".site-footer")) {
    loadFooter();
  }
});

/**
 * Load footer dynamically with proper path resolution
 */
function loadFooter() {
  // Determine the correct path to footer.html based on current location
  const currentPath = window.location.pathname;
  let footerPath = "/footer.html";

  // Adjust path for subdirectories
  if (currentPath.includes("/blog/")) {
    footerPath = "../footer.html";
  } else if (
    currentPath.includes("/tax/") ||
    currentPath.includes("/loan/") ||
    currentPath.includes("/investment/") ||
    currentPath.includes("/health/") ||
    currentPath.includes("/discount/")
  ) {
    footerPath = "../footer.html";
  }

  // Try multiple paths if needed
  const footerPaths = [
    footerPath,
    "/footer.html",
    "./footer.html",
    "../footer.html",
  ];

  function tryLoadFooter(paths, index = 0) {
    if (index >= paths.length) {
      // All paths failed, use fallback
      createFallbackFooter();
      return;
    }

    fetch(paths[index])
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then((data) => {
        document.body.insertAdjacentHTML("beforeend", data);
        // Ensure footer is visible after insertion
        const footer = document.querySelector(".site-footer");
        if (footer) {
          footer.style.opacity = "1";
          footer.style.transition = "opacity 0.3s ease-in-out";
          console.log("Footer loaded successfully from:", paths[index]);
        }
      })
      .catch((error) => {
        console.warn(`Failed to load footer from ${paths[index]}:`, error);
        // Try next path
        tryLoadFooter(paths, index + 1);
      });
  }

  tryLoadFooter(footerPaths);
}

/**
 * Create fallback footer when loading fails
 */
function createFallbackFooter() {
  console.log("Creating fallback footer");
  const fallbackFooter = `
    <footer class="site-footer">
      <div class="container">
        <div class="footer-grid">
          <div class="footer-column">
            <h4>Calculator Suites</h4>
            <p>Free online calculators for all your financial, tax, health, and investment calculation needs.</p>
          </div>
          <div class="footer-column">
            <h4>Calculator Categories</h4>
            <ul class="footer-links">
              <li><a href="../tax/">Tax Calculators</a></li>
              <li><a href="../discount/">Discount Calculators</a></li>
              <li><a href="../investment/">Investment Calculators</a></li>
              <li><a href="../loan/">Loan Calculators</a></li>
              <li><a href="../health/">Health Calculators</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h4>Popular Tools</h4>
            <ul class="footer-links">
              <li><a href="../tax/free-gst-calculator.html">GST Calculator</a></li>
              <li><a href="../investment/free-sip-calculator.html">SIP Calculator</a></li>
              <li><a href="../loan/free-emi-calculator.html">EMI Calculator</a></li>
              <li><a href="../health/free-bmi-calculator.html">BMI Calculator</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h4>Support & Info</h4>
            <ul class="footer-links">
              <li><a href="../contact.html">Contact Us</a></li>
              <li><a href="../privacy.html">Privacy Policy</a></li>
              <li><a href="../how-it-works.html">How It Works</a></li>
              <li><a href="../faq.html">FAQ</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="footer-bottom-content">
            <p>&copy; 2025 CalculatorSuites. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  `;

  document.body.insertAdjacentHTML("beforeend", fallbackFooter);
  const footer = document.querySelector(".site-footer");
  if (footer) {
    footer.style.opacity = "1";
    footer.style.transition = "opacity 0.3s ease-in-out";
  }
}

/**
 * Initialize share functionality for calculator results
 */
function initShareFunctionality() {
  const shareButtons = document.querySelectorAll(".share-results-btn");

  shareButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Get the results container
      const resultsContainer = this.closest(
        ".calculator-container",
      ).querySelector(".results");

      if (resultsContainer) {
        // Get the calculator title
        const calculatorTitle = this.closest(
          ".calculator-container",
        ).querySelector("h2").textContent;

        // Extract key results for sharing
        const shareData = extractShareData(resultsContainer, calculatorTitle);

        // Try to use native Web Share API first
        if (
          navigator.share &&
          navigator.canShare &&
          navigator.canShare(shareData)
        ) {
          navigator.share(shareData).catch((error) => {
            console.log("Error sharing:", error);
            // Fallback to custom share modal
            showShareModal(shareData);
          });
        } else {
          // Fallback to custom share modal
          showShareModal(shareData);
        }
      }
    });
  });
}

/**
 * Extract key data from results for sharing
 */
function extractShareData(resultsContainer, calculatorTitle) {
  const resultRows = resultsContainer.querySelectorAll(".result-row");
  let resultsText = "";

  resultRows.forEach((row) => {
    const label = row.querySelector("span:first-child")?.textContent || "";
    const value = row.querySelector("span:last-child")?.textContent || "";
    if (label && value) {
      resultsText += `${label} ${value}\n`;
    }
  });

  return {
    title: `${calculatorTitle} Results - CalculatorSuites`,
    text: `Check out my calculation results:\n\n${resultsText}\nCalculated using CalculatorSuites`,
    url: window.location.href,
  };
}

/**
 * Show custom share modal when native sharing is not available
 */
function showShareModal(shareData) {
  // Remove existing modal if any
  const existingModal = document.getElementById("share-modal");
  if (existingModal) {
    existingModal.remove();
  }

  // Create share modal
  const modal = document.createElement("div");
  modal.id = "share-modal";
  modal.className = "share-modal";
  modal.innerHTML = `
    <div class="share-modal-content">
      <div class="share-modal-header">
        <h3>Share Your Results</h3>
        <button class="share-modal-close" onclick="closeShareModal()">&times;</button>
      </div>
      <div class="share-modal-body">
        <div class="share-options">
          <button class="share-option" onclick="shareViaWhatsApp('${encodeURIComponent(
            shareData.text + "\n" + shareData.url,
          )}')">
            <span class="share-icon">📱</span>
            WhatsApp
          </button>
          <button class="share-option" onclick="shareViaTwitter('${encodeURIComponent(
            shareData.text,
          )}', '${encodeURIComponent(shareData.url)}')">
            <span class="share-icon">🐦</span>
            Twitter
          </button>
          <button class="share-option" onclick="shareViaFacebook('${encodeURIComponent(
            shareData.url,
          )}')">
            <span class="share-icon">📘</span>
            Facebook
          </button>
          <button class="share-option" onclick="shareViaLinkedIn('${encodeURIComponent(
            shareData.url,
          )}', '${encodeURIComponent(shareData.title)}', '${encodeURIComponent(
    shareData.text,
  )}')">
            <span class="share-icon">💼</span>
            LinkedIn
          </button>
          <button class="share-option" onclick="copyToClipboard('${
            shareData.text
          }\\n${shareData.url}')">
            <span class="share-icon">📋</span>
            Copy Link
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
  modal.style.display = "flex";
}

/**
 * Initialize scroll to top button
 */
function initScrollToTop() {
  // Create scroll to top button if it doesn't exist
  let scrollToTopBtn = document.querySelector(".scroll-to-top");

  if (!scrollToTopBtn) {
    scrollToTopBtn = document.createElement("button");
    scrollToTopBtn.className = "scroll-to-top";
    scrollToTopBtn.innerHTML = "&uarr;";
    scrollToTopBtn.setAttribute("aria-label", "Scroll to top");
    scrollToTopBtn.style.display = "none";
    document.body.appendChild(scrollToTopBtn);
  }

  // Show/hide button based on scroll position
  window.addEventListener("scroll", function () {
    if (window.pageYOffset > 300) {
      scrollToTopBtn.style.display = "block";
    } else {
      scrollToTopBtn.style.display = "none";
    }
  });

  // Scroll to top when button is clicked
  scrollToTopBtn.addEventListener("click", function () {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });
}

/**
 * Share helper functions
 */

// Close share modal
function closeShareModal() {
  const modal = document.getElementById("share-modal");
  if (modal) {
    modal.remove();
  }
}

// Share via WhatsApp
function shareViaWhatsApp(text) {
  const url = `https://wa.me/?text=${text}`;
  window.open(url, "_blank");
}

// Share via Twitter
function shareViaTwitter(text, url) {
  const twitterUrl = `https://twitter.com/intent/tweet?text=${text}&url=${url}`;
  window.open(twitterUrl, "_blank");
}

// Share via Facebook
function shareViaFacebook(url) {
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
  window.open(facebookUrl, "_blank");
}

// Share via LinkedIn
function shareViaLinkedIn(url, title, summary) {
  const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`;
  window.open(linkedInUrl, "_blank");
}

// Copy to clipboard
function copyToClipboard(text) {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showCopySuccess();
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
        fallbackCopyTextToClipboard(text);
      });
  } else {
    fallbackCopyTextToClipboard(text);
  }
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text) {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      showCopySuccess();
    } else {
      console.error("Fallback: Copying text command was unsuccessful");
    }
  } catch (err) {
    console.error("Fallback: Oops, unable to copy", err);
  }

  document.body.removeChild(textArea);
}

// Show copy success message
function showCopySuccess() {
  const message = document.createElement("div");
  message.className = "copy-success-message";
  message.textContent = "Results copied to clipboard!";
  message.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  `;

  document.body.appendChild(message);

  setTimeout(() => {
    if (message.parentNode) {
      message.parentNode.removeChild(message);
    }
  }, 3000);
}
