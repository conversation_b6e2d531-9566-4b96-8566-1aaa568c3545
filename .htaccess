# Enable GZIP compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
  ExpiresActive On

  # Images
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"

  # CSS, JavaScript
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"

  # Fonts
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"

  # HTML - no caching to ensure latest version
  ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Custom error pages
ErrorDocument 404 /404.html

# Prevent directory listing
Options -Indexes

# Redirect www to non-www
RewriteEngine On
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Redirect HTTP to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect from mortgage directory to loan directory
RewriteRule ^mortgage/(.*)$ /loan/$1 [R=301,L]

# Redirect from mortgage index to loan index
RewriteRule ^mortgage/?$ /loan/ [R=301,L]

# Redirect old calculator URLs to new URLs with .html extension (removed conflicting rules)
# Note: Removed trailing slash redirects to prevent canonical conflicts
# Only keeping essential redirects for old URL patterns

# Redirect old percentage calculator URL
RewriteRule ^discount/free-percentage-calculator.html$ /discount/free-percentage.html [R=301,L]

# Essential redirects for old URL patterns (consolidated to prevent conflicts)
RewriteRule ^tax/income-tax.html$ /tax/free-income-tax.html [R=301,L]
RewriteRule ^tax/gst-calculator.html$ /tax/free-gst-calculator.html [R=301,L]
RewriteRule ^tax/tax-comparison.html$ /tax/free-tax-comparison.html [R=301,L]
RewriteRule ^loan/car-loan-emi-calculator.html$ /loan/free-car-loan-emi-calculator.html [R=301,L]
RewriteRule ^loan/mortgage-calculator.html$ /loan/free-mortgage-calculator.html [R=301,L]
RewriteRule ^investment/compound-interest.html$ /investment/free-compound-interest.html [R=301,L]
RewriteRule ^discount/bulk-discount.html$ /discount/free-bulk-discount.html [R=301,L]

# Note: Removed generic trailing slash redirect to prevent conflicts with existing pages

RewriteRule ^loan/bike-loan-emi-calculator.html$ /loan/free-bike-loan-emi-calculator.html [R=301,L]

# Redirect blog URLs with double slashes
RewriteRule ^blog/(.+)//$ /blog/$1.html [R=301,L]

# Redirect non-existent 2025 tax planning article to 2024 version
RewriteRule ^blog/tax-planning-strategies-2025.html$ /blog/tax-planning-strategies-2024.html [R=301,L]

# Additional redirects for remaining 404 errors
RewriteRule ^health/free-pregnancy/$ /health/free-pregnancy.html [R=301,L]
RewriteRule ^loan/free-emi-calculator/$ /loan/free-emi-calculator.html [R=301,L]
RewriteRule ^discount/free-percentage/$ /discount/free-percentage.html [R=301,L]
RewriteRule ^tax/free-tax-refund-calculator/$ /tax/free-tax-refund-calculator.html [R=301,L]
RewriteRule ^health/free-body-fat/$ /health/free-body-fat.html [R=301,L]
RewriteRule ^loan/free-prepayment-calculator/$ /loan/free-prepayment-calculator.html [R=301,L]
RewriteRule ^investment/free-lump-sum/$ /investment/free-lump-sum.html [R=301,L]
RewriteRule ^tax/free-income-tax/$ /tax/free-income-tax.html [R=301,L]
RewriteRule ^tax/free-capital-gains-calculator/$ /tax/free-capital-gains-calculator.html [R=301,L]
RewriteRule ^investment/free-compound-interest/$ /investment/free-compound-interest.html [R=301,L]
RewriteRule ^loan/free-affordability/$ /loan/free-affordability.html [R=301,L]
RewriteRule ^discount/free-amount-based/$ /discount/free-amount-based.html [R=301,L]
RewriteRule ^loan/free-mortgage-calculator/$ /loan/free-mortgage-calculator.html [R=301,L]
RewriteRule ^loan/free-car-loan-emi-calculator/$ /loan/free-car-loan-emi-calculator.html [R=301,L]
RewriteRule ^health/free-calorie-calculator/$ /health/free-calorie-calculator.html [R=301,L]
RewriteRule ^loan/free-bike-loan-emi-calculator/$ /loan/free-bike-loan-emi-calculator.html [R=301,L]
RewriteRule ^blog/calculator-selection-guide/$ /blog/calculator-selection-guide.html [R=301,L]

# Block suspicious/spam URLs
RewriteRule ^[0-9]+/$ - [F,L]
RewriteRule ^[a-z]{5,}$ - [F,L]
RewriteRule ^.*/\?.*=$ - [F,L]
RewriteRule ^3nF/.*$ - [F,L]
RewriteRule ^qhqzf$ - [F,L]
RewriteRule ^egneodunq$ - [F,L]
RewriteRule ^wqk\?.*$ - [F,L]
