﻿<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Deferred Google Analytics - Load after user interaction -->
  <script>
    // Minimal analytics setup - defer full loading
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    // Track initial page view without loading full GTM
    gtag('js', new Date());
    gtag('config', 'G-6BNPSB8DSK', {
      'send_page_view': false // Prevent automatic page view
    });
    // Load Google Analytics after user interaction or 3 seconds
    let analyticsLoaded = false;
    function loadAnalytics() {
      if (analyticsLoaded) return;
      analyticsLoaded = true;
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK';
      document.head.appendChild(script);
      script.onload = function () {
        // Send the page view after analytics loads
        gtag('config', 'G-6BNPSB8DSK', {
          'page_title': document.title,
          'page_location': window.location.href
        });
      };
    }
    // Load analytics on first user interaction
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(function (event) {
      document.addEventListener(event, loadAnalytics, { once: true, passive: true });
    });
    // Fallback: load after 3 seconds if no interaction
    setTimeout(loadAnalytics, 3000);
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="index, follow">
  <title>ELSS Calculator India | Equity Linked Savings Scheme Calculator | CalculatorSuites</title>
  <meta name="description"
    content="Calculate your ELSS mutual fund returns, tax savings, and wealth creation with our free ELSS calculator. 3-year lock-in tax-saving investment planning.">

  <!-- Favicon -->
  <link rel="icon" href="../favicon.svg" type="image/svg+xml">
  <link rel="icon" href="../favicon.ico" type="image/x-icon" sizes="16x16 32x32 48x48">
  <link rel="apple-touch-icon" href="../favicon.svg" sizes="180x180">
  <link rel="manifest" href="../assets/images/site.webmanifest">
  <link rel="preload" href="../assets/js/utils.js" as="script">
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <!-- Preload key font files -->
  <link rel="preload"
    href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2"
    as="font" type="font/woff2" crossorigin>
  <!-- Inline Critical CSS -->
  <style>
    /* Critical CSS - Above the fold styles */
    :root {
      --primary-color: #4361ee;
      --primary-light: #4895ef;
      --primary-dark: #3a0ca3;
      --neutral-100: #f8f9fa;
      --neutral-200: #e9ecef;
      --neutral-800: #343a40;
      --neutral-900: #212529;
      --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    *,
    *::before,
    *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: var(--font-primary);
      font-size: 1rem;
      line-height: 1.5;
      color: var(--neutral-800);
      background-color: var(--neutral-100);
    }

    .site-header {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      flex-direction: column;
    }

    .logo-link {
      text-decoration: none;
    }

    .logo-text {
      font-family: var(--font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--primary-color), #f72585);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      transition: all 0.2s ease;
      padding: 0.5rem;
    }

    .nav-title {
      font-weight: 600;
      font-size: 0.95rem;
      color: var(--neutral-800);
      line-height: 1.2;
    }

    .nav-subtitle {
      font-weight: 400;
      font-size: 0.8rem;
      color: var(--neutral-600);
      line-height: 1.2;
      margin-top: 0.1rem;
    }

    .nav-item:hover .nav-title {
      color: var(--primary-color);
    }

    .nav-item:hover .nav-subtitle {
      color: var(--primary-color);
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-title {
        font-size: 1rem;
      }
      
      .nav-subtitle {
        font-size: 0.85rem;
      }
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      color: var(--neutral-800);
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      padding: 0.5rem 0;
      transition: color 0.2s ease;
      white-space: nowrap;
    }

    .nav-item:hover {
      color: var(--primary-color);
      text-decoration: none;
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }

      .nav-container {
        padding: 1rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-item {
        font-size: 1rem;
      }
    }

    .nav-menu {
      display: none;
      list-style: none;
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .breadcrumb-container {
      background-color: #f8f9fa;
      padding: 0.75rem 0;
    }

    h1 {
      font-family: var(--font-heading);
      font-size: 2.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    /* Hide non-critical content initially - but show calculator */
    .site-footer {
      opacity: 0;
    }

    .calculator-container {
      opacity: 1;
    }
  </style>
  <!-- Load Google Fonts asynchronously -->
  <link rel="preload"
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript>
    <link rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap">
  </noscript>
  <!-- Load non-critical CSS asynchronously -->
  <link rel="preload" href="../assets/css/main.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="../assets/css/calculator.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="../assets/css/responsive.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="../assets/css/footer.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <!-- Fallback for browsers without JS -->
  <noscript>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/calculator.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/footer.css">
  </noscript>
  <!-- Open Graph Tags -->
  <meta property="og:title"
    content="ELSS Calculator | Calculate Equity Linked Savings Scheme Returns | CalculatorSuites">
  <meta property="og:description" content="Calculate ELSS contributions and returns with our free calculator.">
  <meta property="og:url" content="https://www.calculatorsuites.com/indian/elss-calculator.html">
  <meta property="og:type" content="website">
  <meta property="og:image" content="https://www.calculatorsuites.com/assets/images/og-elss-calculator.jpg">
  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title"
    content="ELSS Calculator | Calculate Equity Linked Savings Scheme Returns | CalculatorSuites">
  <meta name="twitter:description" content="Calculate ELSS contributions and returns with our free calculator.">
  <meta name="twitter:image" content="https://www.calculatorsuites.com/assets/images/og-elss-calculator.jpg">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://www.calculatorsuites.com/indian/elss-calculator.html">
  <!-- Schema.org Markup -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Calculate ELSS",
    "description": "Calculate your ELSS mutual fund returns, tax savings, and wealth creation with our free ELSS calculator.",
    "totalTime": "PT2M",
    "tool": {
      "@type": "HowToTool",
      "name": "ELSS Calculator"
    },
    "step": [
      {
        "@type": "HowToStep",
        "name": "Choose Investment Type",
        "text": "Select between Lump Sum investment or SIP (Systematic Investment Plan).",
        "url": "https://www.calculatorsuites.com/indian/elss-calculator.html#step1"
      },
      {
        "@type": "HowToStep",
        "name": "Enter Investment Amount",
        "text": "Enter investment amount (annual for lump sum, monthly for SIP).",
        "url": "https://www.calculatorsuites.com/indian/elss-calculator.html#step2"
      },
      {
        "@type": "HowToStep",
        "name": "Set Investment Period",
        "text": "Enter investment period in years (minimum 3 years lock-in period).",
        "url": "https://www.calculatorsuites.com/indian/elss-calculator.html#step3"
      },
      {
        "@type": "HowToStep",
        "name": "Calculate ELSS Returns",
        "text": "Click Calculate to see projected returns, maturity amount, and tax savings.",
        "url": "https://www.calculatorsuites.com/indian/elss-calculator.html#step4"
      }
    ]
  }
  </script>
  <!-- SoftwareApplication Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "ELSS Calculator | Calculator Suites",
    "applicationCategory": "FinanceTool",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Calculate ELSS amounts for invoices with multiple tax slabs. Supports ELSS inclusive and exclusive calculations with itemized breakdown."
  }
  </script>
  <!-- FAQPage Schema -->
  <script type="application/ld+json">
  {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
      {
        "@type": "Question",
        "name": "What is ELSS and how does it work in India?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "ELSS (Equity Linked Savings Scheme) are mutual funds that invest primarily in equity markets and offer tax benefits under Section 80C. They have the shortest lock-in period (3 years) among tax-saving investments. ELSS funds provide potential for higher returns (10-15% historically) but carry market risk."
        }
      },
      {
        "@type": "Question",
        "name": "What are ELSS investment limits and tax benefits in 2025?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "ELSS investment for tax benefits: Up to ₹1,50,000 under Section 80C. Lock-in period: 3 years (shortest among 80C investments). Tax treatment: Long-term capital gains above ₹1 lakh taxed at 10%. No tax on gains up to ₹1 lakh per year. Dividends are tax-free in investor's hands."
        }
      },
      {
        "@type": "Question",
        "name": "Should I choose ELSS SIP or lump sum investment?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "SIP (Systematic Investment Plan) is generally better for ELSS as it averages out market volatility through rupee cost averaging. SIP helps build discipline and reduces timing risk. Lump sum works well when markets are low or you have surplus funds. Our calculator shows projections for both methods to help you decide."
        }
      }
    ]
}
  </script>
  <!-- BreadcrumbList Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.calculatorsuites.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Tax Calculators",
        "item": "https://www.calculatorsuites.com/tax/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "ELSS Calculator",
        "item": "https://www.calculatorsuites.com/tax/elss-calculator.html"
      }
    ]
  }
  </script>
  <!-- Script to show content after CSS loads -->
  <script>
    // Show hidden content after CSS loads
    function showContent() {
      const hiddenElements = document.querySelectorAll('.site-footer');
      hiddenElements.forEach(el => {
        el.style.opacity = '1';
        el.style.transition = 'opacity 0.3s ease-in-out';
      });
    }
    // Wait for CSS to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(showContent, 100);
      });
    } else {
      setTimeout(showContent, 100);
    }
  </script>
</head>

<body>
  <!-- Header -->
      <header class="site-header">
    <nav class="main-navigation">
      <div class="nav-container">
        <div class="logo">
          <a href="/" class="logo-link">
            <span class="logo-text">Calculator</span>
            <span class="logo-text">Suites</span>
          </a>
        </div>
        <div class="nav-menu">
          <a href="/tax/" class="nav-item">
            <span class="nav-title">Tax</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/discount/" class="nav-item">
            <span class="nav-title">Discount</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/investment/" class="nav-item">
            <span class="nav-title">Investment</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/loan/" class="nav-item">
            <span class="nav-title">Loan</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/health/" class="nav-item">
            <span class="nav-title">Health</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/indian/" class="nav-item">
            <span class="nav-title">Indian</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/blog/" class="nav-item">
            <span class="nav-title">Blog</span>
          </a>
        </div>
        <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>
      </div>
    </nav>
  </header>
  <!-- Main Content -->
  <main class="main-content">
    <div class="container">
      <div class="grid">
        <div class="grid-col-lg-8">
          <!-- Calculator Introduction -->
          <article class="calculator-page">
            <h1>ELSS Calculator: Calculate Equity Linked Savings Scheme Returns and Tax Savings</h1>
            <section class="calculator-intro">
              <p class="lead">Our free ELSS Calculator helps you calculate returns from Equity Linked Savings Scheme
                mutual funds, tax benefits under Section 80C, and wealth creation potential with the shortest lock-in
                period.</p>
              <p>Whether you want to optimize tax savings or plan ELSS investments for wealth creation, this calculator
                provides projected returns based on historical performance, investment amount, and time horizon.</p>
            </section>
            <!-- Calculator Tool -->
            <section class="calculator-tool">
              <div class="calculator-container" id="elss-calculator">
                <h2>ELSS Calculator</h2>
                <form id="elss-calculator-form">

                  <div class="form-group" id="step1">
                    <label for="investment-type">Investment Type:</label>
                    <select id="investment-type" name="investment-type">
                      <option value="lumpsum" selected>Lump Sum Investment</option>
                      <option value="sip">SIP (Systematic Investment Plan)</option>
                    </select>
                    <small class="form-help">Choose your investment method</small>
                  </div>

                  <div class="form-group" id="step2">
                    <label for="investment-amount">Investment Amount (₹):</label>
                    <input type="number" id="investment-amount" name="investment-amount" min="500" max="150000"
                      step="500" placeholder="50000" required>
                    <small class="form-help">Annual amount for lump sum, monthly for SIP</small>
                  </div>

                  <div class="form-group" id="step3">
                    <label for="investment-period">Investment Period (Years):</label>
                    <input type="number" id="investment-period" name="investment-period" min="3" max="30" step="1"
                      placeholder="5" value="5" required>
                    <small class="form-help">Minimum lock-in: 3 years</small>
                  </div>

                  <div class="form-group" id="step4">
                    <label for="expected-return">Expected Annual Return (%):</label>
                    <input type="number" id="expected-return" name="expected-return" min="8" max="20" step="0.5"
                      placeholder="12" value="12">
                    <small class="form-help">Historical ELSS average: 10-15% per annum</small>
                  </div>

                  <div class="form-group">
                    <button type="button" class="btn btn-primary calculate-btn" onclick="calculateELSS()">
                      Calculate ELSS
                    </button>
                  </div>
                </form>
                <div class="results" id="elss-results" style="display: none;">
                  <h3>Results</h3>
                  <div class="result-row">
                    <span>Original Amount:</span>
                    <span id="original-amount">₹0.00</span>
                  </div>
                  <div class="result-row">
                    <span>ELSS Amount:</span>
                    <span id="elss-amount">₹0.00</span>
                  </div>
                  <div class="result-row highlight">
                    <span>Total Amount:</span>
                    <span id="total-amount">₹0.00</span>
                  </div>
                  <button class="share-results-btn">Share Results</button>
                </div>
              </div>
            </section>
            <!-- Calculator Instructions -->
            <section class="calculator-instructions">

              <section class="how-to-use">
                <h2>How to Use This ELSS Calculator</h2>

                <div class="step">
                  <h3>Step 1:</h3>
                  <p>Choose your investment type - Lump Sum (one-time investment) or SIP (monthly systematic investment
                    plan).</p>
                </div>
                <div class="step">
                  <h3>Step 2:</h3>
                  <p>Enter your investment amount. For lump sum, enter the total amount. For SIP, enter the monthly
                    amount.</p>
                </div>
                <div class="step">
                  <h3>Step 3:</h3>
                  <p>Enter your investment period in years. ELSS has a minimum 3-year lock-in period, but you can invest
                    for longer periods.</p>
                </div>
                <div class="step">
                  <h3>Step 4:</h3>
                  <p>Enter your expected annual return percentage. Historical ELSS returns range from 10-15% per annum
                    over long periods.</p>
                </div>
                <div class="step">
                  <h3>Step 5:</h3>
                  <p>Click "Calculate ELSS" to see your projected returns, maturity amount, and tax savings under
                    Section 80C.</p>
                </div>
              </section>
              <section class="calculator-methodology">

                <section class="how-it-works">
                  <h2>How the ELSS Calculator Works</h2>
                  <p>The ELSS Calculator uses different formulas for lump sum and SIP investments. It projects returns
                    based on your expected annual return rate and calculates tax benefits under Section 80C, considering
                    the 3-year lock-in period.</p>
                </section>
                <section class="calculator-use-cases">
                  <h2>Common Uses for ELSS Calculator</h2>
                  <div class="use-case">
                    <h3>Business Invoice Preparation</h3>
                    <p>Businesses use this calculator to prepare accurate invoices by adding ELSS to their product or
                      service
                      prices. For example, if you're selling a product for ₹5,000 and need to add 18% ELSS, the
                      calculator
                      shows the ELSS amount (₹900) and total invoice amount (₹5,900). This ensures compliance with tax
                      regulations and provides transparency to customers about tax components.</p>
                  </div>
                  <div class="use-case">
                    <h3>Retail Price Analysis</h3>
                    <p>Retailers often receive products with ELSS-inclusive pricing and need to understand the tax
                      component
                      for accounting purposes. Using the contribution calculation calculation, they can extract the
                      original price and
                      ELSS amount from the total price. This is essential for proper bookkeeping, profit margin
                      analysis, and
                      understanding the actual cost of goods sold.</p>
                  </div>
                  <div class="use-case">
                    <h3>Tax Planning and Compliance</h3>
                    <p>Accountants and tax professionals use ELSS calculators for tax planning, compliance verification,
                      and
                      audit preparation. The calculator helps verify ELSS amounts on invoices, prepare tax returns, and
                      ensure accurate tax collection and remittance. It's particularly useful when dealing with multiple
                      tax
                      slabs (5%, 12%, 18%, 28%) across different products and services.</p>
                  </div>
                </section>

                <!-- ELSS Information -->
                <section class="calculator-info">
                  <h2>Understanding ELSS (Equity Linked Savings Scheme)</h2>
                  <p>ELSS mutual funds offer tax benefits under Section 80C with the shortest lock-in period of just 3
                    years. They invest primarily in equity markets, providing potential for higher returns compared to
                    traditional tax-saving instruments.</p>

                  <h3>Key ELSS Features</h3>
                  <ul>
                    <li><strong>Investment Limit:</strong> Up to ₹1,50,000 for tax benefits under Section 80C</li>
                    <li><strong>Lock-in Period:</strong> 3 years (shortest among tax-saving investments)</li>
                    <li><strong>Expected Returns:</strong> 10-15% per annum over long term</li>
                    <li><strong>Investment Options:</strong> Lump sum or SIP (Systematic Investment Plan)</li>
                    <li><strong>Tax Treatment:</strong> Long-term capital gains above ₹1 lakh taxed at 10%</li>
                  </ul>

                  <h3>ELSS Investment Strategy</h3>
                  <p>ELSS funds are suitable for investors with moderate to high risk appetite. SIP investments help
                    average out market volatility and are ideal for building long-term wealth while saving taxes.</p>
                </section>

                <div class="grid-col-lg-4">
                  <!-- Sidebar -->
                  <aside class="sidebar">
                    <!-- Related Calculators -->
                    <div class="sidebar-section">
                      <h3>Related Calculators</h3>
                      <ul class="related-calculators">
                        <li><a href="../tax/free-income-tax.html">Income Tax Calculator</a></li>
                        <li><a href="../tax/free-tax-comparison.html">Tax Comparison Tool</a></li>
                        <li><a href="../discount/free-percentage.html">Percentage Discount Calculator</a></li>
                        <li><a href="../investment/free-sip-calculator.html">SIP Calculator</a></li>
                      </ul>
                    </div>
                    <!-- Quick Tips -->
                    <div class="sidebar-section">
                      <h3>ELSS Calculation Tips</h3>
                      <ul class="quick-tips">
                        <li>ELSS has shortest 3-year lock-in among all Section 80C investments.</li>
                        <li>SIP investments help average out market volatility in ELSS funds.</li>
                        <li>ELSS offers potential for higher returns but carries market risk.</li>
                        <li>Long-term capital gains above ₹1 lakh taxed at 10% (as of 2025).</li>
                        <li>Choose ELSS funds with consistent long-term performance track record.</li>
                      </ul>
                    </div>
                  </aside>
                </div>
        </div>
      </div>
  </main>
  <!-- Related Calculators Section -->
  <section class="related-calculators">
    <div class="container">
      <h2 class="section-title">Related Calculators You May Find Useful</h2>
      <div class="calculator-grid">
        <div class="calculator-card">
          <h3><a href="https://www.calculatorsuites.com/tax/free-income-tax.html">Income Tax Calculator</a></h3>
          <p>Calculate your income tax liability under both old and new tax regimes. Essential for tax planning and
            understanding your tax obligations alongside ELSS calculations.</p>
        </div>
        <div class="calculator-card">
          <h3><a href="https://www.calculatorsuites.com/tax/free-tax-comparison.html">Tax Comparison Tool</a></h3>
          <p>Compare PPF returns with ELSS for tax-saving investment planning.</p>
        </div>
        <div class="calculator-card">
          <h3><a href="https://www.calculatorsuites.com/discount/free-percentage.html">Percentage Calculator</a></h3>
          <p>Calculate systematic investment plan returns for various mutual fund categories.</p>
        </div>
      </div>
    </div>
  </section>
  <!-- Load Required Scripts -->
  <script src="../assets/js/utils.js"></script>
  <script src="../assets/js/visual-components.js"></script>
  <script src="../assets/js/calculators/tax.js"></script>
  <script src="../assets/js/faq-enhancement.js"></script>
  <!-- Debug Script -->
  <script>
    console.log('ELSS Calculator page loaded');
    console.log('calculatorUtils available:', typeof calculatorUtils !== 'undefined');
    console.log('storageManager available:', typeof storageManager !== 'undefined');
    // Test if form exists
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.getElementById('elss-calculator-form');
      console.log('ELSS form found:', !!form);
      if (form) {
        form.addEventListener('submit', function (e) {
          console.log('ELSS form submitted');
        });
      }
    });
  </script>
  <script src="../assets/js/main.js" defer></script>

  <script>

    function calculateELSS() {
      // Get input values
      const investmentType = document.getElementById('investment-type').value;
      const investmentAmount = parseFloat(document.getElementById('investment-amount').value);
      const investmentPeriod = parseFloat(document.getElementById('investment-period').value);
      const expectedReturn = parseFloat(document.getElementById('expected-return').value);

      // Validation
      if (!investmentAmount || !investmentPeriod || !expectedReturn) {
        alert('Please fill in all required fields');
        return;
      }

      let maturityAmount;
      let totalInvestment;

      if (investmentType === 'lumpsum') {
        // Lump sum calculation
        totalInvestment = investmentAmount;
        maturityAmount = investmentAmount * Math.pow(1 + expectedReturn / 100, investmentPeriod);
      } else {
        // SIP calculation
        const monthlyAmount = investmentAmount;
        const monthlyRate = expectedReturn / 100 / 12;
        const totalMonths = investmentPeriod * 12;

        totalInvestment = monthlyAmount * totalMonths;
        maturityAmount = monthlyAmount * ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate) * (1 + monthlyRate);
      }

      const totalReturns = maturityAmount - totalInvestment;
      const taxSaved = Math.min(totalInvestment, 150000) * 0.3; // Assuming 30% tax bracket
      const annualizedReturn = ((maturityAmount / totalInvestment) ** (1 / investmentPeriod) - 1) * 100;

      // Display results
      const resultsDiv = document.getElementById('elss-results');
      resultsDiv.innerHTML = `
        <h3>ELSS Calculation Results</h3>
        <div class="result-item">
          <span class="result-label">Investment Type:</span>
          <span class="result-value">${investmentType === 'lumpsum' ? 'Lump Sum' : 'SIP (Monthly)'}</span>
        </div>
        <div class="result-item">
          <span class="result-label">Total Investment:</span>
          <span class="result-value">₹${totalInvestment.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
        </div>
        <div class="result-item">
          <span class="result-label">Total Returns:</span>
          <span class="result-value">₹${totalReturns.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
        </div>
        <div class="result-item highlight">
          <span class="result-label">Maturity Amount:</span>
          <span class="result-value">₹${maturityAmount.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
        </div>
        <div class="result-item">
          <span class="result-label">Tax Saved (Approx.):</span>
          <span class="result-value">₹${taxSaved.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
        </div>
        <div class="result-item">
          <span class="result-label">Annualized Return:</span>
          <span class="result-value">${annualizedReturn.toFixed(2)}%</span>
        </div>
      `;
      resultsDiv.style.display = 'block';
    }
  </script>
</body>

</html>