Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FEBA
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210285FF9, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBF80  0002100690B4 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFC260  00021006A49D (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFF4180000 ntdll.dll
7FFFF33C0000 KERNEL32.DLL
7FFFF19F0000 KERNELBASE.dll
7FFFF3740000 USER32.dll
7FFFF19C0000 win32u.dll
7FFFF3710000 GDI32.dll
7FFFF1890000 gdi32full.dll
7FFFF1DF0000 msvcp_win.dll
7FFFF1510000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFF2430000 advapi32.dll
7FFFF2080000 msvcrt.dll
7FFFF3490000 sechost.dll
7FFFF2310000 RPCRT4.dll
7FFFF0920000 CRYPTBASE.DLL
7FFFF1EA0000 bcryptPrimitives.dll
7FFFF2040000 IMM32.DLL
