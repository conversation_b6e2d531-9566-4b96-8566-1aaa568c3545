﻿<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-6BNPSB8DSK"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-6BNPSB8DSK');
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="index, follow">
  <title>GST ITC Calculator: Complete Guide to Input Tax Credit Calculation 2024</title>
  <meta name="description"
    content="Master GST Input Tax Credit calculations with our comprehensive ITC calculator guide. Learn eligibility, rules, and optimize your tax credit claims effectively.">
  
  <!-- Bing Site Verification -->
  <meta name="msvalidate.01" content="9529EBE6E2CEA3A47DB13958B783A792" />
  <!-- Favicon -->
  <link rel="icon" href="../favicon.svg" type="image/svg+xml">
  <link rel="icon" href="../favicon.ico" type="image/x-icon" sizes="16x16 32x32 48x48">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
  <link rel="stylesheet" href="../assets/css/calculator.css">
  <link rel="stylesheet" href="../assets/css/responsive.css">
  <link rel="stylesheet" href="../assets/css/footer.css">
  <!-- Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap"
    rel="stylesheet">
  <!-- Open Graph Tags -->
  <meta property="og:title" content="GST ITC Calculator: Complete Guide to Input Tax Credit Calculation 2024">
  <meta property="og:description"
    content="Master GST Input Tax Credit calculations with our comprehensive ITC calculator guide. Learn eligibility and rules.">
  <meta property="og:url" content="https://www.calculatorsuites.com/blog/gst-itc-calculator-guide\/">
  <meta property="og:type" content="article">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://www.calculatorsuites.com/blog/gst-itc-calculator-guide.html">
  <!-- Article Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "GST ITC Calculator: Complete Guide to Input Tax Credit Calculation 2024",
    "description": "Master GST Input Tax Credit calculations with our comprehensive ITC calculator guide. Learn eligibility, rules, and optimize your tax credit claims effectively.",
    "author": {
      "@type": "Person",
      "name": "Venkatesh Rao",
      "url": "https://www.calculatorsuites.com/about"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Calculator Suites",
      "url": "https://www.calculatorsuites.com"
    },
    "datePublished": "2025-01-19",
    "dateModified": "2025-01-19",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.calculatorsuites.com/blog/gst-itc-calculator-guide\/"
    }
  }
  </script>

    <style>
      .site-header {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .logo {
      display: flex;
      flex-direction: column;
    }

    .logo-link {
      text-decoration: none;
    }

    .logo-text {
      font-family: var(--font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, var(--primary-color), #f72585);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      transition: all 0.2s ease;
      padding: 0.5rem;
    }

    .nav-title {
      font-weight: 600;
      font-size: 0.95rem;
      color: var(--neutral-800);
      line-height: 1.2;
    }

    .nav-subtitle {
      font-weight: 400;
      font-size: 0.8rem;
      color: var(--neutral-600);
      line-height: 1.2;
      margin-top: 0.1rem;
    }

    .nav-item:hover .nav-title {
      color: var(--primary-color);
    }

    .nav-item:hover .nav-subtitle {
      color: var(--primary-color);
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-title {
        font-size: 1rem;
      }
      
      .nav-subtitle {
        font-size: 0.85rem;
      }
    }

    .nav-menu {
      display: none;
      gap: 2rem;
      align-items: center;
    }

    .nav-item {
      color: var(--neutral-800);
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      padding: 0.5rem 0;
      transition: color 0.2s ease;
      white-space: nowrap;
    }

    .nav-item:hover {
      color: var(--primary-color);
      text-decoration: none;
    }

    .mobile-menu-toggle {
      display: block;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.5rem;
      color: var(--neutral-800);
    }

    @media (min-width: 768px) {
      .mobile-menu-toggle {
        display: none;
      }

      .nav-menu {
        display: flex;
      }

      .logo-text {
        font-size: 1.75rem;
      }

      .nav-container {
        padding: 1rem;
      }
    }

    @media (min-width: 1024px) {
      .nav-menu {
        gap: 2.5rem;
      }
      
      .nav-item {
        font-size: 1rem;
      }
    }
      
      .nav-menu {
        display: flex;
        gap: 30px;
        align-items: center;
      }
      
      .nav-item {
        text-decoration: none;
        color: #374151;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
        min-width: 80px;
      }
      
      .nav-item:hover {
        background-color: #f3f4f6;
        color: #6366f1;
      }
      
      .nav-title {
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
      }
      
      .nav-subtitle {
        font-size: 11px;
        color: #6b7280;
        line-height: 1.1;
        margin-top: 1px;
      }
      
      .nav-item:hover .nav-subtitle {
        color: #6366f1;
      }
      
      @media (max-width: 768px) {
        .nav-container {
          padding: 0 15px;
          height: 60px;
        }
        
        .nav-menu {
          gap: 15px;
        }
        
        .nav-item {
          padding: 6px 8px;
          min-width: 60px;
        }
        
        .nav-title {
          font-size: 12px;
        }
        
        .nav-subtitle {
          font-size: 10px;
        }
        
        .logo-text {
          font-size: 16px;
        }
      }
      
      @media (max-width: 480px) {
        .nav-menu {
          gap: 10px;
        }
        
        .nav-item {
          padding: 4px 6px;
          min-width: 50px;
        }
        
        .nav-title {
          font-size: 11px;
        }
        
        .nav-subtitle {
          display: none;
        }
      }
    </style>
</head>

<body>
  <!-- Header -->
        <header class="site-header">
    <nav class="main-navigation">
      <div class="nav-container">
        <div class="logo">
          <a href="/" class="logo-link">
            <span class="logo-text">Calculator</span>
            <span class="logo-text">Suites</span>
          </a>
        </div>
        <div class="nav-menu">
          <a href="/tax/" class="nav-item">
            <span class="nav-title">Tax</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/discount/" class="nav-item">
            <span class="nav-title">Discount</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/investment/" class="nav-item">
            <span class="nav-title">Investment</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/loan/" class="nav-item">
            <span class="nav-title">Loan</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/health/" class="nav-item">
            <span class="nav-title">Health</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/indian/" class="nav-item">
            <span class="nav-title">Indian</span>
            <span class="nav-subtitle">Calculators</span>
          </a>
          <a href="/blog/" class="nav-item">
            <span class="nav-title">Blog</span>
          </a>
        </div>
        <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>
      </div>
    </nav>
  </header>
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="container">
      <h1 class="hero-title">GST ITC Calculator</h1>
      <p class="hero-subtitle">Master Input Tax Credit calculations with our comprehensive guide. Understand
        eligibility, rules, and optimize your GST credit claims for maximum tax savings.</p>
      <div class="hero-meta">
        <span class="author">By Venkatesh Rao</span>
        <span class="date">January 19, 2025</span>
        <span class="read-time">15 min read</span>
      </div>
    </div>
  </section>
  <!-- Main Content -->
  <div class="container">
    <div class="main-content">
      <article class="article-content">
        <div class="toc-box">
          <h3>📋 Table of Contents</h3>
          <ul class="toc-list">
            <li><a href="#what-is-gst-itc">What is GST Input Tax Credit?</a></li>
            <li><a href="#eligibility-conditions">Eligibility Conditions</a></li>
            <li><a href="#calculator-demo">Try Our ITC Calculator</a></li>
            <li><a href="#eligible-transactions">Eligible Transactions</a></li>
            <li><a href="#blocked-credits">Blocked Credits</a></li>
            <li><a href="#calculation-process">Calculation Process</a></li>
            <li><a href="#reversal-scenarios">Reversal Scenarios</a></li>
            <li><a href="#time-limits">Time Limits & Deadlines</a></li>
            <li><a href="#compliance-tips">Compliance Tips</a></li>
          </ul>
        </div>
        <section id="what-is-gst-itc">
          <h2>What is GST Input Tax Credit (ITC)?</h2>
          <p><strong>GST Input Tax Credit (ITC)</strong> is the credit that a business can claim on the GST paid on
            purchases made for business purposes. This credit can be used to reduce the GST liability on sales,
            effectively avoiding double taxation.</p>
          <div class="highlight-card">
            <h3>💡 Key Benefit</h3>
            <p>ITC reduces your overall GST liability, helping you save money and maintain healthy cash flow for your
              business operations.</p>
          </div>
          <div class="info-box">
            <h4>🔍 How ITC Works</h4>
            <p>If you pay ₹1,800 GST on purchases (input) and collect ₹3,000 GST on sales (output), you only need to pay
              ₹1,200 (₹3,000 - ₹1,800) to the government. The ₹1,800 is your Input Tax Credit.</p>
          </div>
          <p>ITC is available for:</p>
          <ul>
            <li>Goods and services used for business purposes</li>
            <li>Capital goods and plant & machinery</li>
            <li>Input services like transportation, legal services</li>
            <li>Goods used in manufacturing or trading</li>
          </ul>
        </section>
        <section id="eligibility-conditions">
          <h2>Eligibility Conditions for ITC</h2>
          <div class="warning-box">
            <h4>⚠️ Important Requirements</h4>
            <p>All conditions must be met simultaneously to claim ITC. Missing even one condition can result in
              disallowance of the credit.</p>
          </div>
          <div class="step-process">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>Valid Tax Invoice</h4>
                <p>You must have a valid tax invoice or debit note issued by the supplier.</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>Goods/Services Received</h4>
                <p>You must have received the goods or services or both for business purposes.</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>Tax Payment by Supplier</h4>
                <p>The supplier must have paid the tax to the government (except reverse charge cases).</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>Return Filing</h4>
                <p>You must file the GST return within the prescribed time limit.</p>
              </div>
            </div>
          </div>
        </section>
        <section id="calculator-demo">
          <h2>Try Our GST ITC Calculator</h2>
          <div class="calculator-demo">
            <h3>🧮 Calculate Your Input Tax Credit</h3>
            <div class="demo-inputs">
              <div class="input-group">
                <label for="purchase-amount">Purchase Amount (?)</label>
                <input type="number" id="purchase-amount" placeholder="e.g., 50000" value="50000">
              </div>
              <div class="input-group">
                <label for="gst-rate">GST Rate (%)</label>
                <select id="gst-rate">
                  <option value="5">5%</option>
                  <option value="12">12%</option>
                  <option value="18" selected>18%</option>
                  <option value="28">28%</option>
                </select>
              </div>
              <div class="input-group">
                <label for="business-use">Business Use (%)</label>
                <input type="number" id="business-use" placeholder="e.g., 80" value="100" min="0" max="100">
              </div>
            </div>
            <button class="calc-button" onclick="calculateITC()">Calculate ITC</button>
            <div id="itc-result"
              style="margin-top: 1rem; padding: 1rem; background: #f0f8ff; border-radius: 8px; display: none;">
              <h4>ITC Details:</h4>
              <div id="itc-details"></div>
            </div>
          </div>
          <div style="text-align: center; margin-top: 2rem;">
            <a href="../tax/free-gst-calculator.html" class="cta-button">Use Advanced GST Calculator</a>
          </div>
        </section>
        <section id="eligible-transactions">
          <h2>Eligible vs Blocked Transactions</h2>
          <div class="eligibility-grid">
            <div class="eligible-card">
              <h3>✅ Eligible for ITC</h3>
              <ul class="feature-list">
                <li>Raw materials for manufacturing</li>
                <li>Capital goods and machinery</li>
                <li>Office supplies and equipment</li>
                <li>Professional services</li>
                <li>Transportation services</li>
                <li>Rent for business premises</li>
                <li>Advertising and marketing</li>
                <li>Insurance for business assets</li>
              </ul>
            </div>
            <div class="blocked-card">
              <h3>❌ Blocked Credits</h3>
              <ul class="feature-list blocked-list">
                <li>Motor vehicles for transportation of persons</li>
                <li>Food and beverages for employees</li>
                <li>Membership of clubs and gyms</li>
                <li>Travel benefits to employees</li>
                <li>Personal consumption goods</li>
                <li>Gifts and free samples</li>
                <li>Construction services for immovable property</li>
                <li>Services for personal use</li>
              </ul>
            </div>
          </div>
        </section>
        <section id="calculation-process">
          <h2>ITC Calculation Process</h2>
          <div class="calculator-demo">
            <h3>📊 Step-by-Step Calculation</h3>
            <table class="itc-table">
              <thead>
                <tr>
                  <th>Step</th>
                  <th>Description</th>
                  <th>Formula</th>
                  <th>Example</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>Calculate GST on Purchase</td>
                  <td>Purchase Amount ? GST Rate</td>
                  <td>₹50,000 ? 18% = ₹9,000</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>Determine Business Use</td>
                  <td>GST ? Business Use %</td>
                  <td>₹9,000 ? 80% = ₹7,200</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>Check Eligibility</td>
                  <td>Apply blocked credit rules</td>
                  <td>₹7,200 (if eligible)</td>
                </tr>
                <tr>
                  <td>4</td>
                  <td>Claim ITC</td>
                  <td>Add to ITC ledger</td>
                  <td>₹7,200 credit available</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>
        <section id="reversal-scenarios">
          <h2>ITC Reversal Scenarios</h2>
          <div class="comparison-grid">
            <div class="comparison-card">
              <h3>🔄 Common Reversal Cases</h3>
              <ul class="feature-list">
                <li>Goods become exempt</li>
                <li>Used for non-business purposes</li>
                <li>Supplier cancels registration</li>
                <li>Invoice found to be fake</li>
                <li>Goods lost, stolen, or destroyed</li>
                <li>Service not received</li>
              </ul>
            </div>
            <div class="comparison-card">
              <h3>📋 Reversal Rules</h3>
              <ul class="feature-list">
                <li>Proportionate reversal for partial use</li>
                <li>Full reversal if entirely non-business</li>
                <li>Time-based reversal for capital goods</li>
                <li>Interest on reversed amount</li>
                <li>Penalty for willful misuse</li>
                <li>Re-availment when use changes</li>
              </ul>
            </div>
          </div>
          <div class="info-box">
            <h4>🔄 Reversal Calculation</h4>
            <p><strong>Formula:</strong> ITC to be reversed = (Exempt supplies / Total supplies) ? ITC availed</p>
            <p><strong>Example:</strong> If 30% of your supplies become exempt, reverse 30% of the ITC claimed on
              inputs.</p>
          </div>
        </section>
        <section id="time-limits">
          <h2>Time Limits & Deadlines</h2>
          <table class="itc-table">
            <thead>
              <tr>
                <th>Action</th>
                <th>Time Limit</th>
                <th>Consequences of Delay</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Claim ITC</td>
                <td>Earlier of: Annual return due date OR 1 year from invoice date</td>
                <td>ITC becomes time-barred</td>
              </tr>
              <tr>
                <td>Reverse ITC</td>
                <td>Return of the month when liability arises</td>
                <td>Interest @ 18% p.a.</td>
              </tr>
              <tr>
                <td>Rectify errors</td>
                <td>November 30 of next financial year</td>
                <td>Cannot make corrections</td>
              </tr>
              <tr>
                <td>Respond to notices</td>
                <td>As specified in the notice</td>
                <td>Ex-parte proceedings</td>
              </tr>
            </tbody>
          </table>
        </section>
        <section id="compliance-tips">
          <h2>ITC Compliance Best Practices</h2>
          <div class="highlight-card">
            <h3>💡 Expert Tips for ITC Management</h3>
            <ul style="text-align: left; margin-top: 1rem;">
              <li><strong>Maintain Proper Records:</strong> Keep all invoices, receipts, and supporting documents</li>
              <li><strong>Regular Reconciliation:</strong> Match ITC with supplier returns monthly</li>
              <li><strong>Timely Filing:</strong> File returns within due dates to avoid time-barring</li>
              <li><strong>Monitor Supplier Compliance:</strong> Ensure suppliers pay tax to government</li>
              <li><strong>Segregate Purchases:</strong> Clearly identify business vs personal use</li>
              <li><strong>Stay Updated:</strong> Keep track of rule changes and clarifications</li>
            </ul>
          </div>
        </section>
        <div class="calculator-demo">
          <h3>📊 ITC Calculation Examples</h3>
          <table class="itc-table">
            <thead>
              <tr>
                <th>Transaction</th>
                <th>Purchase Amount</th>
                <th>GST Rate</th>
                <th>Business Use</th>
                <th>ITC Available</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Office Equipment</td>
                <td>₹1,00,000</td>
                <td>18%</td>
                <td>100%</td>
                <td>₹18,000</td>
              </tr>
              <tr>
                <td>Vehicle (Mixed Use)</td>
                <td>₹5,00,000</td>
                <td>28%</td>
                <td>60%</td>
                <td>₹84,000</td>
              </tr>
              <tr>
                <td>Raw Material</td>
                <td>₹2,00,000</td>
                <td>12%</td>
                <td>100%</td>
                <td>₹24,000</td>
              </tr>
              <tr>
                <td>Professional Services</td>
                <td>₹50,000</td>
                <td>18%</td>
                <td>100%</td>
                <td>₹9,000</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="warning-box">
          <h4>⚠️ Common ITC Mistakes to Avoid</h4>
          <ul>
            <li>Claiming ITC without proper invoices</li>
            <li>Not verifying supplier's GST payment</li>
            <li>Claiming blocked credits</li>
            <li>Delay in ITC reversal when required</li>
            <li>Not maintaining proper documentation</li>
            <li>Claiming ITC on personal purchases</li>
          </ul>
        </div>
        <div class="cta-section">
          <h2>Optimize Your ITC Claims</h2>
          <p>Use our comprehensive GST tools to calculate, track, and optimize your Input Tax Credit claims for maximum
            tax savings.</p>
          <a href="../tax/free-gst-calculator.html" class="cta-button">Start GST Calculations</a>
        </div>
      </article>
      </main>
      <aside class="sidebar">
        <div class="quick-links">
          <h3>🛠️ GST Tools</h3>
          <div class="links-grid">
            <a href="../tax/free-gst-calculator.html" class="quick-link">
              <strong>GST Calculator</strong><br>
              <small>Calculate GST & ITC</small>
            </a>
            <a href="../tax/free-income-tax.html" class="quick-link">
              <strong>Income Tax Calculator</strong><br>
              <small>Calculate income tax</small>
            </a>
            <a href="../tax/free-capital-gains-calculator.html" class="quick-link">
              <strong>Capital Gains Tax</strong><br>
              <small>Calculate capital gains</small>
            </a>
            <a href="../tax/free-tax-refund-calculator.html" class="quick-link">
              <strong>Tax Refund Calculator</strong><br>
              <small>Calculate refunds</small>
            </a>
          </div>
        </div>
        <div class="author-bio">
          <h3>💡 ITC Tip</h3>
          <p>Always verify that your supplier has paid the GST to the government before claiming ITC. Use GSTN portal to
            check supplier compliance.</p>
        </div>
        <div class="quick-links">
          <h3>📚 Related Articles</h3>
          <div class="links-grid">
            <a href="gst-adjustment-calculator-guide\/" class="quick-link">
              <strong>GST Adjustment Calculator</strong><br>
              <small>Tax adjustments guide</small>
            </a>
            <a href="vehicle-loan-emi-calculator-guide\/" class="quick-link">
              <strong>Vehicle Loan EMI</strong><br>
              <small>Calculate vehicle EMI</small>
            </a>
            <a href="lump-sum-calculator-guide\/" class="quick-link">
              <strong>Lump Sum Calculator</strong><br>
              <small>Investment planning</small>
            </a>
          </div>
        </div>
      </aside>
    </div>
  </div>
  <script>
    function calculateITC() {
      const purchaseAmount = parseFloat(document.getElementById('purchase-amount').value);
      const gstRate = parseFloat(document.getElementById('gst-rate').value);
      const businessUse = parseFloat(document.getElementById('business-use').value);
      if (!purchaseAmount || !gstRate || !businessUse) {
        alert('Please fill in all fields');
        return;
      }
      const totalGST = (purchaseAmount * gstRate) / 100;
      const availableITC = (totalGST * businessUse) / 100;
      const nonBusinessGST = totalGST - availableITC;
      document.getElementById('itc-result').style.display = 'block';
      document.getElementById('itc-details').innerHTML = `
        <p><strong>Purchase Amount:</strong> ?${purchaseAmount.toFixed(2)}</p>
        <p><strong>Total GST:</strong> ?${totalGST.toFixed(2)}</p>
        <p><strong>Available ITC:</strong> ?${availableITC.toFixed(2)}</p>
        <p><strong>Non-Business GST:</strong> ?${nonBusinessGST.toFixed(2)}</p>
        <p style="margin-top: 1rem; font-weight: bold; color: #28a745;">
          You can claim ?${availableITC.toFixed(2)} as Input Tax Credit
        </p>
      `;
    }
  </script>
  <script src="../assets/js/utils.js"></script>\n
  <script src="assets/js/main.js" defer></script>
</body>

</html>